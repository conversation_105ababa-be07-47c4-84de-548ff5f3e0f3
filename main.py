import asyncio
import os
from browser_use.browser.session import BrowserSession
from browser_use.llm.google import ChatGoogle
llm = ChatGoogle(model="gemini-2.0-flash")

async def main():
    # Certifique-se de que a variável de ambiente GOOGLE_API_KEY está definida
    if "GOOGLE_API_KEY" not in os.environ:
        print("Erro: A variável de ambiente GOOGLE_API_KEY não está definida.")
        print("Por favor, defina-a antes de executar este script.")
        return

    api_key = os.environ["GOOGLE_API_KEY"]
    
    # Inicializa o BrowserUse com o modelo Gemini 2.5 Flash
    browser = BrowserSession(llm=llm)
    try:
        await browser.start()
        # Exemplo de uso: navegar para uma página e interagir com ela
        print(f"Valor de browser.agent_current_page: {browser.agent_current_page}")
        await browser.agent_current_page.goto("https://www.google.com")
        print("Navegou para o Google.")

        # Exemplo de interação: preencher a barra de pesquisa e clicar no botão
        # await browser.type("input[name='q']", "BrowserUse library")
        # await browser.click("input[name='btnK']")
        # print("Pesquisou por 'BrowserUse library'.")

        # Você pode adicionar mais interações aqui, como obter o conteúdo da página, etc.
        # content = await browser.page.content()
        # print(f"Conteúdo da página: {content[:500]}...")

    except Exception as e:
        print(f"Ocorreu um erro: {e}")
    finally:
        # Garante que o navegador seja fechado
        await browser.close()
        print("Navegador fechado.")

if __name__ == "__main__":
    asyncio.run(main())