#!/usr/bin/env python3
"""
Exemplo Prático - Tarefa com Restrições de Domínio
Este script demonstra como executar uma tarefa com domínios restritos.
"""

import json
import requests
import time

def test_restricted_task():
    """Testa uma tarefa real com restrições de domínio"""
    print("🚀 Testando tarefa com restrições de domínio...")
    
    # Configuração da tarefa com domínios restritos
    task_config = {
        "task": "Pesquisar informações sobre Python programming language, focando em conceitos básicos e aplicações",
        "config": {
            "model": "gpt-4o",
            "provider": "openai",
            "temperature": 0.0,
            "headless": False,
            "use_vision": True,
            "max_steps": 30,
            "api_key": "your-api-key-here",
            "allowed_domains": [
                "python.org",
                "docs.python.org",
                "realpython.com",
                "wikipedia.org"
            ],
            "system_prompt": "",
            "user_data_dir": "",
            "disable_security": True,
            "window_width": 1280,
            "window_height": 720
        }
    }
    
    print("📋 Configuração da tarefa:")
    print(f"  Tarefa: {task_config['task']}")
    print(f"  Modelo: {task_config['config']['model']}")
    print(f"  Domínios permitidos: {', '.join(task_config['config']['allowed_domains'])}")
    
    # Simular envio para API (descomente para testar real)
    """
    try:
        response = requests.post(
            'http://localhost:3000/api/browser-use',
            json=task_config,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['task']['id']
            print(f"✅ Tarefa criada com ID: {task_id}")
            
            # Monitorar progresso
            monitor_task_progress(task_id)
            
        else:
            print(f"❌ Erro ao criar tarefa: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")
    """
    
    # Para demonstração, vamos simular o comportamento esperado
    simulate_restricted_execution(task_config)

def simulate_restricted_execution(config):
    """Simula a execução com restrições para demonstração"""
    print("\n🎭 Simulando execução com restrições...")
    
    allowed_domains = config['config']['allowed_domains']
    
    # URLs que o agente tentaria visitar
    attempted_urls = [
        "https://python.org",  # ✅ Permitido
        "https://docs.python.org/3/tutorial/",  # ✅ Permitido
        "https://realpython.com/python-basics/",  # ✅ Permitido
        "https://en.wikipedia.org/wiki/Python_(programming_language)",  # ✅ Permitido
        "https://stackoverflow.com/questions/tagged/python",  # ❌ Bloqueado
        "https://github.com/python/cpython",  # ❌ Bloqueado
        "https://www.google.com/search?q=python",  # ❌ Bloqueado
    ]
    
    print("\n🔍 Simulando tentativas de navegação:")
    
    for url in attempted_urls:
        from urllib.parse import urlparse
        
        # Simular validação de domínio
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        if domain.startswith('www.'):
            domain = domain[4:]
        
        is_allowed = False
        for allowed_domain in allowed_domains:
            allowed_domain_clean = allowed_domain.lower()
            if allowed_domain_clean.startswith('www.'):
                allowed_domain_clean = allowed_domain_clean[4:]
            
            if domain == allowed_domain_clean or domain.endswith('.' + allowed_domain_clean):
                is_allowed = True
                break
        
        status = "✅ PERMITIDO" if is_allowed else "🚫 BLOQUEADO"
        print(f"  {status} | {url}")
        
        if is_allowed:
            print(f"    📄 Extraindo conteúdo de {domain}...")
            time.sleep(0.5)  # Simular tempo de carregamento
        else:
            print(f"    🛡️ Acesso negado por restrição de domínio")

def monitor_task_progress(task_id):
    """Monitora o progresso de uma tarefa real"""
    print(f"\n👀 Monitorando progresso da tarefa {task_id}...")
    
    max_attempts = 30
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f'http://localhost:3000/api/browser-use?id={task_id}')
            
            if response.status_code == 200:
                task_data = response.json()['task']
                status = task_data['status']
                
                print(f"📊 Status: {status}")
                
                # Mostrar últimas mensagens
                messages = task_data.get('messages', [])
                if messages:
                    last_message = messages[-1]
                    print(f"💬 Última atividade: {last_message['content']}")
                
                # Mostrar URL atual se disponível
                if 'currentUrl' in task_data and task_data['currentUrl']:
                    print(f"🌐 URL atual: {task_data['currentUrl']}")
                
                if status in ['completed', 'failed']:
                    print(f"🏁 Tarefa finalizada com status: {status}")
                    
                    # Mostrar estatísticas finais
                    if 'duration' in task_data:
                        duration = task_data['duration'] / 1000  # Convert to seconds
                        print(f"⏱️ Duração: {duration:.1f}s")
                    
                    screenshots_count = len(task_data.get('screenshots', []))
                    print(f"📸 Screenshots capturados: {screenshots_count}")
                    
                    break
                
            else:
                print(f"❌ Erro ao consultar tarefa: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ Erro de monitoramento: {e}")
            break
        
        attempt += 1
        time.sleep(2)  # Aguardar 2 segundos antes da próxima verificação
    
    if attempt >= max_attempts:
        print("⏰ Timeout no monitoramento da tarefa")

def create_test_scenarios():
    """Cria diferentes cenários de teste"""
    print("\n🧪 Cenários de teste disponíveis:")
    
    scenarios = [
        {
            "name": "Pesquisa Acadêmica",
            "task": "Pesquisar sobre machine learning algorithms",
            "domains": ["scholar.google.com", "arxiv.org", "ieee.org"]
        },
        {
            "name": "Desenvolvimento Web",
            "task": "Buscar informações sobre React.js",
            "domains": ["reactjs.org", "developer.mozilla.org", "w3schools.com"]
        },
        {
            "name": "Notícias Tecnológicas",
            "task": "Coletar últimas notícias sobre IA",
            "domains": ["techcrunch.com", "wired.com", "arstechnica.com"]
        },
        {
            "name": "Documentação Oficial",
            "task": "Estudar documentação do Python",
            "domains": ["python.org", "docs.python.org"]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   Tarefa: {scenario['task']}")
        print(f"   Domínios: {', '.join(scenario['domains'])}")

def main():
    """Função principal"""
    print("🎯 Exemplo Prático - Restrições de Domínio")
    print("=" * 50)
    
    # Mostrar cenários disponíveis
    create_test_scenarios()
    
    print("\n" + "=" * 50)
    
    # Executar teste principal
    test_restricted_task()
    
    print("\n💡 Para testar com a API real:")
    print("1. Inicie o servidor: npm run dev")
    print("2. Configure sua API key no código")
    print("3. Descomente a seção de requisição HTTP")
    print("4. Execute: python3 example_restricted_task.py")
    
    print("\n🔧 Para personalizar:")
    print("1. Modifique a lista 'allowed_domains'")
    print("2. Altere a tarefa conforme necessário")
    print("3. Ajuste os parâmetros do modelo")

if __name__ == "__main__":
    main()