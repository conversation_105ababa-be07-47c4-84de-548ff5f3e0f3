# Browser-Use Web Interface

Uma interface web completa para o [browser-use](https://github.com/browser-use/browser-use) que replica todas as funcionalidades do CLI em uma interface moderna e intuitiva.

## 🚀 Funcionalidades

### ✨ Interface Completa como o CLI
- **Chat Interativo**: Interface de chat em tempo real com o agente AI
- **Configuração Avançada**: Configure modelos, API keys, e parâmetros do browser
- **Histórico de Comandos**: Navegue pelo histórico com ↑/↓ como no CLI
- **Visualização em Tempo Real**: Veja screenshots do browser durante a automação

### 🤖 Suporte a Múltiplos LLMs
- **OpenAI**: GPT-4o, GPT-4o Mini, GPT-4 Turbo, GPT-3.5 Turbo
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Opus, Claude 3 Haiku
- **Google**: Gemini 2.0 Flash, Gemini 1.5 Pro, Gemini 1.5 Flash

### 🌐 Configuração Avançada do Browser
- **Mo<PERSON> Headless/Visual**: Escolha entre execução visível ou em background
- **Configurações de Janela**: Defina largura e altura da janela
- **Perfis Personalizados**: Use perfis existentes do Chrome
- **Domínios Permitidos**: Restrinja navegação a domínios específicos

### 🎯 Configuração do Agente
- **Visão AI**: Habilite/desabilite análise visual de páginas
- **Máximo de Passos**: Configure quantos passos o agente pode executar
- **Prompt Personalizado**: Adicione instruções específicas para o agente
- **Controle de Temperatura**: Ajuste criatividade vs precisão

### 📊 Monitoramento em Tempo Real
- **Status da Tarefa**: Acompanhe o progresso em tempo real
- **Screenshots Automáticos**: Veja o que o agente está fazendo
- **Logs Detalhados**: Mensagens detalhadas de cada passo
- **Metadados**: URLs visitadas, elementos encontrados, ações executadas

## 🛠️ Instalação e Configuração

### Pré-requisitos

1. **Node.js** (versão 18 ou superior)
2. **Python 3.8+** com browser-use instalado
3. **API Keys** para os LLMs que deseja usar

### Instalação do Browser-Use

```bash
# Instalar browser-use com todas as dependências
pip install browser-use[cli]

# Ou se preferir apenas a biblioteca base
pip install browser-use

# Instalar Playwright (necessário para automação do browser)
playwright install
```

### Configuração do Projeto Web

```bash
# Clone o repositório
git clone <repository-url>
cd browser-use-web

# Instalar dependências
npm install

# Executar em modo de desenvolvimento
npm run dev
```

### Configuração das API Keys

1. Acesse a aba **⚙️ Config** na interface web
2. Selecione seu provedor preferido (OpenAI, Anthropic, ou Google)
3. Insira sua API key
4. Configure outros parâmetros conforme necessário
5. Clique em **Save** para salvar as configurações

Alternativamente, você pode definir as variáveis de ambiente:

```bash
# Para OpenAI
export OPENAI_API_KEY="sua-api-key-aqui"

# Para Anthropic
export ANTHROPIC_API_KEY="sua-api-key-aqui"

# Para Google
export GOOGLE_API_KEY="sua-api-key-aqui"
```

## 🎮 Como Usar

### 1. Interface Principal (Chat)

1. Abra [http://localhost:3000](http://localhost:3000)
2. Configure suas API keys na aba **⚙️ Config**
3. Digite sua tarefa na caixa de texto
4. Pressione Enter ou clique em **Send**
5. Acompanhe o progresso em tempo real

**Exemplos de tarefas:**
- "Pesquise por notícias sobre IA no Google"
- "Acesse o site da OpenAI e encontre informações sobre o GPT-4"
- "Vá para o GitHub e procure por repositórios de machine learning"
- "Abra o LinkedIn e veja as últimas postagens"

### 2. Visualização do Browser

- Acesse a aba **🖥️ Browser** para ver screenshots em tempo real
- Veja a URL atual e título da página
- Acompanhe o status da tarefa (pending, running, completed, failed)

### 3. Configuração Avançada

Na aba **⚙️ Config**, você pode configurar:

#### LLM Configuration
- **Provider**: OpenAI, Anthropic, ou Google
- **Model**: Escolha o modelo específico
- **Temperature**: Controle criatividade (0.0 = focado, 1.0 = criativo)
- **API Key**: Sua chave de API

#### Browser Configuration
- **Window Size**: Largura e altura da janela
- **Headless Mode**: Executar sem interface visual
- **User Data Directory**: Usar perfil existente do Chrome
- **Disable Security**: Desabilitar recursos de segurança para melhor automação

#### Agent Configuration
- **Max Steps**: Máximo de passos que o agente pode executar
- **Use Vision**: Habilitar análise visual de páginas
- **System Prompt**: Instruções personalizadas para o agente

#### Advanced Settings
- **Allowed Domains**: Restringir navegação a domínios específicos

### 4. Histórico de Comandos

- Acesse a aba **📜 History** para ver tarefas anteriores
- Use ↑/↓ no campo de texto para navegar pelo histórico
- Clique em qualquer tarefa do histórico para reutilizá-la

## 🔧 Funcionalidades Avançadas

### MCP (Model Context Protocol)

Acesse `/mcp` para usar funcionalidades avançadas com MCP:
- Integração com filesystem
- Integração com GitHub
- Operações de banco de dados
- Capacidades estendidas de automação

### API Endpoints

A aplicação expõe várias APIs REST:

- `POST /api/browser-use` - Criar nova tarefa
- `GET /api/browser-use?id={taskId}` - Obter status da tarefa
- `DELETE /api/browser-use?id={taskId}` - Cancelar tarefa
- `POST /api/mcp-task` - Criar tarefa com MCP

### Configuração Persistente

Todas as configurações são salvas automaticamente no localStorage:
- Configurações do LLM e browser
- Histórico de comandos
- Preferências da interface

## 🐛 Solução de Problemas

### Erro: "browser-use not available"
```bash
# Instalar browser-use
pip install browser-use[cli]
```

### Erro: "Playwright not found"
```bash
# Instalar Playwright
playwright install
```

### Erro: "API key not found"
1. Configure sua API key na aba Config
2. Ou defina a variável de ambiente correspondente

### Browser não abre
1. Verifique se o Playwright está instalado
2. Tente desabilitar o modo headless
3. Verifique se não há outros processos do Chrome rodando

### Tarefas ficam "pending"
1. Verifique os logs no console do navegador
2. Confirme que o Python está no PATH
3. Verifique se todas as dependências estão instaladas

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🙏 Agradecimentos

- [browser-use](https://github.com/browser-use/browser-use) - A biblioteca principal de automação
- [Next.js](https://nextjs.org/) - Framework React
- [Tailwind CSS](https://tailwindcss.com/) - Framework CSS
- [Radix UI](https://www.radix-ui.com/) - Componentes UI primitivos
