/**
 * Script para limpar arquivos temporários de tarefas do Browser-Use
 * 
 * Este script pode ser executado de duas formas:
 * 1. Manualmente: node cleanup_temp_files.js
 * 2. Com opções: node cleanup_temp_files.js --all (remove todos os arquivos de tarefas)
 *                node cleanup_temp_files.js --age=10 (remove arquivos mais antigos que 10 minutos)
 */
const fs = require('fs');
const path = require('path');

// Processar argumentos da linha de comando
const args = process.argv.slice(2);
const removeAll = args.includes('--all');
let timeLimit = 60 * 60 * 1000; // Padrão: 1 hora

// Verificar se há um argumento de idade personalizado
const ageArg = args.find(arg => arg.startsWith('--age='));
if (ageArg) {
  const minutes = parseInt(ageArg.split('=')[1], 10);
  if (!isNaN(minutes) && minutes > 0) {
    timeLimit = minutes * 60 * 1000;
    console.log(`Configurado para remover arquivos mais antigos que ${minutes} minutos`);
  }
}

// Diretório onde os arquivos temporários são armazenados
const directory = process.cwd();

// Padrão para identificar arquivos de tarefas
const taskFilePattern = /browser_use_task_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\.py$/;

// Obter a lista de arquivos no diretório
fs.readdir(directory, (err, files) => {
  if (err) {
    console.error('Erro ao ler o diretório:', err);
    return;
  }

  const now = Date.now();
  let removedCount = 0;

  // Filtrar e remover arquivos de tarefas antigos
  files.forEach(file => {
    if (taskFilePattern.test(file)) {
      const filePath = path.join(directory, file);
      
      // Se a opção --all foi especificada, remover todos os arquivos de tarefas
      if (removeAll) {
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error(`Erro ao remover arquivo ${file}:`, unlinkErr);
          } else {
            removedCount++;
            console.log(`Removido: ${file}`);
          }
        });
        return;
      }
      
      // Verificar a data de modificação do arquivo
      fs.stat(filePath, (statErr, stats) => {
        if (statErr) {
          console.error(`Erro ao verificar arquivo ${file}:`, statErr);
          return;
        }

        const fileAge = now - stats.mtimeMs;
        
        // Remover arquivos mais antigos que o limite de tempo
        if (fileAge > timeLimit) {
          fs.unlink(filePath, (unlinkErr) => {
            if (unlinkErr) {
              console.error(`Erro ao remover arquivo ${file}:`, unlinkErr);
            } else {
              removedCount++;
              console.log(`Removido: ${file} (idade: ${Math.round(fileAge / 60000)} minutos)`);
            }
          });
        }
      });
    }
  });

  // Mostrar resumo após a conclusão
  setTimeout(() => {
    console.log(`\nLimpeza concluída. ${removedCount} arquivos temporários removidos.`);
  }, 1000);
});