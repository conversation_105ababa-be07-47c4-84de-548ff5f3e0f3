#!/usr/bin/env python3
"""
Advanced browser task runner that replicates browser-use CLI functionality
with intelligent page analysis, element detection, and action execution.
"""

import asyncio
import json
import sys
import time
import re
import base64
from typing import Dict, List, Any, Optional, Tuple
import subprocess
import os

try:
    from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

class ElementInfo:
    """Information about a clickable element on the page"""
    def __init__(self, index: int, tag: str, text: str, xpath: str, bbox: Dict[str, float]):
        self.index = index
        self.tag = tag
        self.text = text
        self.xpath = xpath
        self.bbox = bbox
        
class PageState:
    """Current state of the browser page"""
    def __init__(self, url: str, title: str, elements: List[ElementInfo], screenshot_b64: str = ""):
        self.url = url
        self.title = title
        self.elements = elements
        self.screenshot_b64 = screenshot_b64

class AdvancedBrowserRunner:
    def __init__(self, task_id: str, task: str):
        self.task_id = task_id
        self.task = task
        self.messages: List[Dict[str, Any]] = []
        self.progress = 0.0
        self.page_state: Optional[PageState] = None
        
    def add_message(self, message_type: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to the task progress"""
        message = {
            "id": f"msg_{len(self.messages)}",
            "type": message_type,
            "content": content,
            "timestamp": time.time() * 1000,
            "metadata": metadata or {}
        }
        self.messages.append(message)
        
        # Print progress for CLI
        progress_data = {
            "task_id": self.task_id,
            "status": "running",
            "messages": self.messages,
            "progress": self.progress
        }
        print(f"PROGRESS:{json.dumps(progress_data)}")
        sys.stdout.flush()

    async def extract_page_elements(self, page: Page) -> List[ElementInfo]:
        """Extract clickable elements from the page using advanced JavaScript (like browser-use)"""
        try:
            # Advanced JavaScript code that mimics browser-use element detection
            js_code = """
            () => {
                const elements = [];
                let index = 0;

                // Comprehensive selectors for all interactive elements
                const selectors = [
                    // Basic interactive elements
                    'a[href]', 'button', 'input', 'select', 'textarea',
                    // Role-based elements
                    '[role="button"]', '[role="link"]', '[role="menuitem"]', '[role="tab"]',
                    '[role="option"]', '[role="checkbox"]', '[role="radio"]',
                    // Event handlers
                    '[onclick]', '[onmousedown]', '[onmouseup]', '[onchange]',
                    // Focusable elements
                    '[tabindex]:not([tabindex="-1"])',
                    // Form related
                    'label[for]', 'legend',
                    // Content editable
                    '[contenteditable="true"]',
                    // Custom interactive elements
                    '.btn', '.button', '.link', '.clickable',
                    // Google specific
                    '[jsaction]', '[data-ved]'
                ];

                // Get all potentially interactive elements
                const allElements = new Set();
                selectors.forEach(selector => {
                    try {
                        document.querySelectorAll(selector).forEach(el => allElements.add(el));
                    } catch (e) {
                        // Skip invalid selectors
                    }
                });

                // Also find elements that look clickable based on CSS
                document.querySelectorAll('*').forEach(el => {
                    const style = window.getComputedStyle(el);
                    if (style.cursor === 'pointer' ||
                        style.cursor === 'hand' ||
                        el.style.cursor === 'pointer') {
                        allElements.add(el);
                    }
                });

                allElements.forEach(el => {
                    try {
                        // Skip hidden elements
                        const style = window.getComputedStyle(el);
                        if (style.display === 'none' ||
                            style.visibility === 'hidden' ||
                            style.opacity === '0' ||
                            el.offsetWidth === 0 ||
                            el.offsetHeight === 0) {
                            return;
                        }

                        const rect = el.getBoundingClientRect();
                        if (rect.width === 0 || rect.height === 0) return;

                        // Skip elements outside viewport (but keep some margin)
                        if (rect.bottom < -100 || rect.top > window.innerHeight + 100 ||
                            rect.right < -100 || rect.left > window.innerWidth + 100) {
                            return;
                        }

                        // Get comprehensive element text
                        let text = '';

                        // Try different text sources
                        if (el.textContent && el.textContent.trim()) {
                            text = el.textContent.trim();
                        } else if (el.innerText && el.innerText.trim()) {
                            text = el.innerText.trim();
                        } else if (el.value && el.value.trim()) {
                            text = el.value.trim();
                        } else {
                            // Try attributes
                            text = el.getAttribute('placeholder') ||
                                   el.getAttribute('title') ||
                                   el.getAttribute('aria-label') ||
                                   el.getAttribute('alt') ||
                                   el.getAttribute('data-tooltip') ||
                                   el.getAttribute('aria-describedby') || '';
                        }

                        // For inputs, include type information
                        if (el.tagName.toLowerCase() === 'input') {
                            const type = el.getAttribute('type') || 'text';
                            const name = el.getAttribute('name') || '';
                            const id = el.getAttribute('id') || '';
                            text = `${text} [${type}${name ? ' name=' + name : ''}${id ? ' id=' + id : ''}]`.trim();
                        }

                        // Generate robust XPath
                        function getXPath(element) {
                            if (element.id && element.id.trim()) {
                                return `//*[@id="${element.id}"]`;
                            }

                            // Try to build a more specific path
                            const parts = [];
                            let current = element;

                            while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
                                let part = current.tagName.toLowerCase();

                                // Add class if available and not too generic
                                if (current.className && typeof current.className === 'string') {
                                    const classes = current.className.trim().split(/\\s+/);
                                    const specificClasses = classes.filter(cls =>
                                        cls.length > 2 &&
                                        !cls.match(/^(btn|button|link|item|element|container|wrapper)$/i)
                                    );
                                    if (specificClasses.length > 0) {
                                        part += `[contains(@class,"${specificClasses[0]}")]`;
                                    }
                                }

                                // Add position if needed
                                if (current.parentNode) {
                                    const siblings = Array.from(current.parentNode.children).filter(
                                        child => child.tagName === current.tagName
                                    );
                                    if (siblings.length > 1) {
                                        const position = siblings.indexOf(current) + 1;
                                        part += `[${position}]`;
                                    }
                                }

                                parts.unshift(part);
                                current = current.parentNode;
                            }

                            return '//' + parts.join('/');
                        }

                        // Get element attributes for better identification
                        const attributes = {};
                        ['id', 'class', 'name', 'type', 'role', 'aria-label', 'data-ved', 'jsaction'].forEach(attr => {
                            const value = el.getAttribute(attr);
                            if (value) attributes[attr] = value;
                        });

                        elements.push({
                            index: index++,
                            tag: el.tagName.toLowerCase(),
                            text: text.substring(0, 150), // Increased text length
                            xpath: getXPath(el),
                            bbox: {
                                x: Math.round(rect.x),
                                y: Math.round(rect.y),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            },
                            attributes: attributes,
                            isVisible: rect.top >= 0 && rect.top <= window.innerHeight
                        });
                    } catch (e) {
                        // Skip problematic elements
                    }
                });

                // Sort by visibility and position (visible elements first, then by position)
                elements.sort((a, b) => {
                    if (a.isVisible && !b.isVisible) return -1;
                    if (!a.isVisible && b.isVisible) return 1;
                    return a.bbox.y - b.bbox.y;
                });

                return elements;
            }
            """

            elements_data = await page.evaluate(js_code)
            elements = []

            for elem_data in elements_data:
                elements.append(ElementInfo(
                    index=elem_data['index'],
                    tag=elem_data['tag'],
                    text=elem_data['text'],
                    xpath=elem_data['xpath'],
                    bbox=elem_data['bbox']
                ))

            return elements

        except Exception as e:
            self.add_message("system", f"Error extracting elements: {str(e)}", {"action": "error"})
            return []

    async def take_screenshot(self, page: Page) -> str:
        """Take a high-quality screenshot and return as base64"""
        try:
            # Try PNG first for best quality
            screenshot_bytes = await page.screenshot(
                type='png',
                full_page=True,  # Capture full page for better context
                clip=None
            )
            screenshot_b64 = base64.b64encode(screenshot_bytes).decode('utf-8')

            # If too large, try JPEG with high quality
            if len(screenshot_b64) > 800000:  # 800KB limit
                screenshot_bytes = await page.screenshot(
                    type='jpeg',
                    quality=85,  # High quality JPEG
                    full_page=True
                )
                screenshot_b64 = base64.b64encode(screenshot_bytes).decode('utf-8')

                # If still too large, reduce quality slightly
                if len(screenshot_b64) > 600000:  # 600KB limit
                    screenshot_bytes = await page.screenshot(
                        type='jpeg',
                        quality=70,  # Good quality JPEG
                        full_page=True
                    )
                    screenshot_b64 = base64.b64encode(screenshot_bytes).decode('utf-8')

            return screenshot_b64
        except Exception as e:
            self.add_message("system", f"Error taking screenshot: {str(e)}", {"action": "error"})
            return ""

    async def analyze_page_state(self, page: Page) -> PageState:
        """Analyze current page state"""
        try:
            url = page.url
            title = await page.title()
            elements = await self.extract_page_elements(page)
            screenshot_b64 = await self.take_screenshot(page)
            
            self.page_state = PageState(url, title, elements, screenshot_b64)
            
            self.add_message("system", f"Analyzed page: {title}", {
                "action": "page_analysis",
                "url": url,
                "elements_found": len(elements)
            })
            
            return self.page_state
            
        except Exception as e:
            self.add_message("system", f"Error analyzing page: {str(e)}", {"action": "error"})
            return PageState("", "", [], "")

    async def click_element(self, page: Page, index: int) -> bool:
        """Click an element by index"""
        try:
            if not self.page_state or index >= len(self.page_state.elements):
                self.add_message("system", f"Element index {index} not found", {"action": "error"})
                return False
            
            element_info = self.page_state.elements[index]
            self.add_message("system", f"Clicking element {index}: {element_info.text[:50]}", {
                "action": "click",
                "index": index,
                "text": element_info.text
            })
            
            # Try to find element by XPath
            try:
                element = await page.locator(f"xpath={element_info.xpath}").first
                await element.click(timeout=5000)
                await asyncio.sleep(2)
                return True
            except:
                # Fallback to coordinate click
                bbox = element_info.bbox
                center_x = bbox['x'] + bbox['width'] / 2
                center_y = bbox['y'] + bbox['height'] / 2
                await page.mouse.click(center_x, center_y)
                await asyncio.sleep(2)
                return True
                
        except Exception as e:
            self.add_message("system", f"Error clicking element {index}: {str(e)}", {"action": "error"})
            return False

    async def input_text(self, page: Page, index: int, text: str) -> bool:
        """Input text into an element by index"""
        try:
            if not self.page_state or index >= len(self.page_state.elements):
                self.add_message("system", f"Element index {index} not found", {"action": "error"})
                return False
            
            element_info = self.page_state.elements[index]
            self.add_message("system", f"Inputting text into element {index}: {text}", {
                "action": "input",
                "index": index,
                "text": text
            })
            
            # Find and fill element
            element = await page.locator(f"xpath={element_info.xpath}").first
            await element.click()
            await element.fill(text)
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            self.add_message("system", f"Error inputting text into element {index}: {str(e)}", {"action": "error"})
            return False

    async def intelligent_action_decision(self, page: Page, task: str) -> bool:
        """Make intelligent decisions about what actions to take based on the task and page state"""
        try:
            await self.analyze_page_state(page)
            
            if not self.page_state:
                return False
            
            task_lower = task.lower()
            
            # Login page detection and handling
            if any(keyword in self.page_state.title.lower() or keyword in task_lower 
                   for keyword in ['login', 'entrar', 'sign in', 'authentication']):
                return await self.handle_login_page(page, task)
            
            # Form filling detection
            form_elements = [e for e in self.page_state.elements if e.tag in ['input', 'textarea', 'select']]
            if form_elements and ('preencher' in task_lower or 'fill' in task_lower):
                return await self.handle_form_filling(page, task)
            
            # Search functionality
            if 'pesquisar' in task_lower or 'search' in task_lower:
                return await self.handle_search(page, task)
            
            # Click specific elements
            if 'clicar' in task_lower or 'click' in task_lower:
                return await self.handle_clicking(page, task)
            
            # General exploration
            return await self.explore_page_intelligently(page)
            
        except Exception as e:
            self.add_message("system", f"Error in intelligent decision making: {str(e)}", {"action": "error"})
            return False

    async def handle_login_page(self, page: Page, task: str) -> bool:
        """Handle login page interactions"""
        try:
            self.add_message("system", "Detected login page - analyzing form fields", {"action": "login_detection"})
            
            # Find username/email and password fields
            username_element = None
            password_element = None
            
            for element in self.page_state.elements:
                if element.tag == 'input':
                    # Check for username/email fields
                    if any(keyword in element.xpath.lower() or keyword in element.text.lower() 
                           for keyword in ['user', 'email', 'login', 'utilizador']):
                        username_element = element
                    # Check for password fields
                    elif 'password' in element.xpath.lower() or 'pass' in element.xpath.lower():
                        password_element = element
            
            if username_element and password_element:
                self.add_message("system", f"Found login form - username field at index {username_element.index}, password field at index {password_element.index}", {
                    "action": "login_form_found",
                    "username_index": username_element.index,
                    "password_index": password_element.index
                })
                
                # For demo purposes, we'll just indicate the form was found
                # In a real implementation, you'd extract credentials from the task
                self.add_message("assistant", "Login form detected. Please provide credentials to continue.", {
                    "action": "user_input_needed",
                    "form_type": "login"
                })
                return True
            else:
                self.add_message("system", "Could not identify standard login fields", {"action": "login_fields_not_found"})
                return False
                
        except Exception as e:
            self.add_message("system", f"Error handling login page: {str(e)}", {"action": "error"})
            return False

    async def handle_form_filling(self, page: Page, task: str) -> bool:
        """Handle automatic form filling"""
        try:
            self.add_message("system", "Analyzing form fields for auto-filling", {"action": "form_analysis"})

            form_elements = [e for e in self.page_state.elements if e.tag in ['input', 'textarea', 'select']]
            filled_count = 0

            for element in form_elements:
                # Smart field detection and filling
                field_text = element.text.lower() + element.xpath.lower()

                if any(keyword in field_text for keyword in ['email', 'e-mail']):
                    await self.input_text(page, element.index, '<EMAIL>')
                    filled_count += 1
                elif any(keyword in field_text for keyword in ['name', 'nome']):
                    await self.input_text(page, element.index, 'João Silva')
                    filled_count += 1
                elif any(keyword in field_text for keyword in ['phone', 'telefone', 'telemovel']):
                    await self.input_text(page, element.index, '+351 912 345 678')
                    filled_count += 1
                elif any(keyword in field_text for keyword in ['address', 'morada', 'endereco']):
                    await self.input_text(page, element.index, 'Rua das Flores, 123, Lisboa')
                    filled_count += 1

            if filled_count > 0:
                self.add_message("system", f"Auto-filled {filled_count} form fields", {
                    "action": "form_filled",
                    "fields_filled": filled_count
                })

                # Look for submit button
                submit_buttons = [e for e in self.page_state.elements
                                if e.tag == 'button' or (e.tag == 'input' and 'submit' in e.xpath.lower())]

                if submit_buttons:
                    self.add_message("system", f"Found submit button, clicking...", {"action": "submit_form"})
                    await self.click_element(page, submit_buttons[0].index)

                return True
            else:
                self.add_message("system", "No recognizable form fields found for auto-filling", {"action": "form_not_filled"})
                return False

        except Exception as e:
            self.add_message("system", f"Error handling form filling: {str(e)}", {"action": "error"})
            return False

    async def handle_search(self, page: Page, task: str) -> bool:
        """Handle search functionality"""
        try:
            self.add_message("system", "Looking for search functionality", {"action": "search_detection"})

            # Find search input fields
            search_elements = []
            for element in self.page_state.elements:
                if element.tag == 'input':
                    field_text = element.text.lower() + element.xpath.lower()
                    if any(keyword in field_text for keyword in ['search', 'pesquisa', 'busca', 'procurar']):
                        search_elements.append(element)

            if search_elements:
                search_element = search_elements[0]

                # Extract search term from task
                search_term = self.extract_search_term_from_task(task)

                self.add_message("system", f"Found search field, searching for: {search_term}", {
                    "action": "search_found",
                    "search_term": search_term
                })

                await self.input_text(page, search_element.index, search_term)

                # Press Enter or find search button
                try:
                    await page.keyboard.press('Enter')
                    await asyncio.sleep(3)
                except:
                    # Look for search button
                    search_buttons = [e for e in self.page_state.elements
                                    if e.tag == 'button' and any(keyword in e.text.lower()
                                    for keyword in ['search', 'pesquisar', 'buscar'])]
                    if search_buttons:
                        await self.click_element(page, search_buttons[0].index)

                return True
            else:
                self.add_message("system", "No search functionality found", {"action": "search_not_found"})
                return False

        except Exception as e:
            self.add_message("system", f"Error handling search: {str(e)}", {"action": "error"})
            return False

    def extract_search_term_from_task(self, task: str) -> str:
        """Extract search term from task description"""
        task_lower = task.lower()

        # Look for patterns like "search for X", "pesquisar X", etc.
        patterns = [
            r'pesquisar?\s+(?:por\s+)?(.+?)(?:\s+no|\s+em|\s*$)',
            r'search\s+(?:for\s+)?(.+?)(?:\s+on|\s+in|\s*$)',
            r'procurar?\s+(?:por\s+)?(.+?)(?:\s+no|\s+em|\s*$)',
            r'buscar?\s+(?:por\s+)?(.+?)(?:\s+no|\s+em|\s*$)'
        ]

        for pattern in patterns:
            match = re.search(pattern, task_lower)
            if match:
                return match.group(1).strip()

        # Fallback: look for quoted terms or specific keywords
        if 'sigo' in task_lower:
            return 'sigo portugal'

        return 'exemplo'

    async def handle_clicking(self, page: Page, task: str) -> bool:
        """Handle clicking on specific elements"""
        try:
            self.add_message("system", "Analyzing clickable elements", {"action": "click_analysis"})

            # Look for specific elements mentioned in the task
            task_lower = task.lower()

            # Find relevant clickable elements
            clickable_elements = [e for e in self.page_state.elements
                                if e.tag in ['a', 'button'] or 'click' in e.xpath.lower()]

            if clickable_elements:
                # Try to find the most relevant element
                best_element = None
                best_score = 0

                for element in clickable_elements:
                    score = 0
                    element_text = element.text.lower()

                    # Score based on task keywords
                    if 'login' in task_lower and any(keyword in element_text for keyword in ['login', 'entrar', 'sign in']):
                        score += 10
                    elif 'submit' in task_lower and any(keyword in element_text for keyword in ['submit', 'enviar', 'confirmar']):
                        score += 10
                    elif 'next' in task_lower and any(keyword in element_text for keyword in ['next', 'seguinte', 'proximo']):
                        score += 10

                    # General relevance
                    if len(element_text) > 0:
                        score += 1

                    if score > best_score:
                        best_score = score
                        best_element = element

                if best_element:
                    self.add_message("system", f"Clicking most relevant element: {best_element.text[:50]}", {
                        "action": "click_selected",
                        "element_text": best_element.text
                    })
                    await self.click_element(page, best_element.index)
                    return True
                else:
                    # Click first clickable element as fallback
                    self.add_message("system", f"Clicking first clickable element: {clickable_elements[0].text[:50]}", {
                        "action": "click_fallback"
                    })
                    await self.click_element(page, clickable_elements[0].index)
                    return True
            else:
                self.add_message("system", "No clickable elements found", {"action": "no_clickable_elements"})
                return False

        except Exception as e:
            self.add_message("system", f"Error handling clicking: {str(e)}", {"action": "error"})
            return False

    async def explore_page_intelligently(self, page: Page) -> bool:
        """Intelligently explore the page"""
        try:
            self.add_message("system", "Exploring page content intelligently", {"action": "page_exploration"})

            # Provide detailed page analysis
            clickable_count = len([e for e in self.page_state.elements if e.tag in ['a', 'button']])
            form_count = len([e for e in self.page_state.elements if e.tag in ['input', 'textarea', 'select']])

            self.add_message("assistant", f"Page Analysis Complete:\n" +
                           f"• Title: {self.page_state.title}\n" +
                           f"• URL: {self.page_state.url}\n" +
                           f"• Clickable elements: {clickable_count}\n" +
                           f"• Form fields: {form_count}\n" +
                           f"• Total interactive elements: {len(self.page_state.elements)}", {
                "action": "exploration_complete",
                "page_stats": {
                    "title": self.page_state.title,
                    "url": self.page_state.url,
                    "clickable_elements": clickable_count,
                    "form_fields": form_count,
                    "total_elements": len(self.page_state.elements)
                }
            })

            return True

        except Exception as e:
            self.add_message("system", f"Error exploring page: {str(e)}", {"action": "error"})
            return False

    async def run_task(self):
        """Main method to run the browser task with advanced automation"""
        try:
            self.add_message("system", f"Starting advanced browser task: {self.task}")

            if not PLAYWRIGHT_AVAILABLE:
                self.add_message("system", "Playwright not available. Please install it with: pip install playwright && playwright install", {"action": "error"})
                return

            async with async_playwright() as p:
                browser = None
                try:
                    # Launch browser with advanced settings
                    self.add_message("system", "Launching browser with advanced automation capabilities...", {"action": "init", "browser": "chromium"})
                    browser = await p.chromium.launch(
                        headless=False,
                        slow_mo=500,  # Moderate speed for better reliability
                        args=[
                            '--start-maximized',
                            '--disable-blink-features=AutomationControlled',
                            '--disable-web-security',
                            '--disable-features=VizDisplayCompositor',
                            '--disable-dev-shm-usage',
                            '--no-sandbox'
                        ]
                    )

                    # Create new page with advanced settings
                    self.add_message("system", "Creating new browser tab...", {"action": "open_tab"})
                    page = await browser.new_page()

                    # Set realistic browser properties
                    await page.set_extra_http_headers({
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept-Language': 'pt-PT,pt;q=0.9,en;q=0.8'
                    })

                    await page.set_viewport_size({"width": 1280, "height": 720})

                    # Parse task and determine strategy
                    task_lower = self.task.lower()

                    # Check if it's a direct URL navigation
                    if "http" in self.task or "www." in self.task:
                        success = await self.handle_direct_url_navigation(page)
                    else:
                        # Search-based navigation
                        success = await self.handle_search_based_navigation(page)

                    if success:
                        # Perform intelligent actions based on the task
                        await self.intelligent_action_decision(page, self.task)

                    # Final page analysis
                    await self.analyze_page_state(page)

                    # Mark task as completed
                    self.progress = 1.0
                    self.add_message("assistant", f"Task completed successfully! Final page: {page.url}", {
                        "action": "task_complete",
                        "final_url": page.url,
                        "final_title": await page.title()
                    })

                    # Keep browser open for user to see results
                    self.add_message("system", "Keeping browser open for 15 seconds to view results...", {"action": "info"})
                    await asyncio.sleep(15)

                except Exception as e:
                    self.add_message("system", f"Error during task execution: {str(e)}", {"action": "error"})
                finally:
                    if browser:
                        await browser.close()
                        self.add_message("system", "Browser closed", {"action": "cleanup"})

        except Exception as e:
            self.add_message("system", f"Fatal error: {str(e)}", {"action": "fatal_error"})

    async def handle_direct_url_navigation(self, page: Page) -> bool:
        """Handle direct URL navigation"""
        try:
            # Extract URL from task
            url_pattern = r'https?://[^\s]+|www\.[^\s]+'
            urls = re.findall(url_pattern, self.task)

            if urls:
                target_url = urls[0]
                if not target_url.startswith('http'):
                    target_url = 'https://' + target_url

                self.add_message("system", f"Navigating directly to: {target_url}", {
                    "action": "direct_navigation",
                    "url": target_url
                })

                await page.goto(target_url, wait_until="domcontentloaded", timeout=30000)
                await asyncio.sleep(3)

                current_url = page.url
                title = await page.title()

                self.add_message("system", f"Successfully navigated to {current_url}", {
                    "action": "navigation_success",
                    "url": current_url,
                    "title": title
                })

                return True

            return False

        except Exception as e:
            self.add_message("system", f"Error in direct URL navigation: {str(e)}", {"action": "error"})
            return False

    async def handle_search_based_navigation(self, page: Page) -> bool:
        """Handle search-based navigation using Google or DuckDuckGo"""
        try:
            # Determine search query
            if "sigo" in self.task.lower():
                search_query = "sigo portugal"
                target_site = "sigo.pt"
            else:
                search_query = self.task
                target_site = None

            # Try Google first
            self.add_message("system", "Navigating to Google...", {"action": "navigate", "url": "https://www.google.com"})

            try:
                await page.goto("https://www.google.com", wait_until="domcontentloaded", timeout=15000)
                await asyncio.sleep(3)

                # Check for CAPTCHA or blocking
                if "sorry" in page.url.lower() or "captcha" in page.url.lower():
                    self.add_message("system", "Google is blocking access, switching to DuckDuckGo...", {"action": "switch_engine"})
                    return await self.search_with_duckduckgo(page, search_query, target_site)

                return await self.search_with_google(page, search_query, target_site)

            except Exception as google_error:
                self.add_message("system", f"Google failed, trying DuckDuckGo: {str(google_error)}", {"action": "fallback"})
                return await self.search_with_duckduckgo(page, search_query, target_site)

        except Exception as e:
            self.add_message("system", f"Error in search-based navigation: {str(e)}", {"action": "error"})
            return False

    async def search_with_google(self, page: Page, search_query: str, target_site: str = None) -> bool:
        """Perform search using Google with robust element detection"""
        try:
            # First analyze the current page to see what elements are available
            self.add_message("system", "Analyzing Google page elements...", {"action": "page_analysis"})
            await self.analyze_page_state(page)

            # Show found elements for debugging (like CLI does)
            self.add_message("system", f"Found {len(self.page_state.elements)} interactive elements on Google", {
                "action": "elements_found",
                "count": len(self.page_state.elements)
            })

            # List some key elements found
            for i, element in enumerate(self.page_state.elements[:5]):
                self.add_message("system", f"Element {i}: {element.tag} - '{element.text[:30]}'", {
                    "action": "element_info",
                    "index": i,
                    "tag": element.tag,
                    "text": element.text[:30]
                })

            # Accept cookies if present
            cookie_buttons = [e for e in self.page_state.elements
                            if e.tag == 'button' and any(word in e.text.lower()
                            for word in ['accept', 'aceitar', 'agree', 'concordo'])]

            if cookie_buttons:
                self.add_message("system", f"Clicking cookie accept button: {cookie_buttons[0].text}", {"action": "cookie_accept"})
                await self.click_element(page, cookie_buttons[0].index)
                await asyncio.sleep(2)
                # Re-analyze page after cookie acceptance
                await self.analyze_page_state(page)

            # Find search input with multiple strategies
            search_element = None

            # Strategy 1: Look for input elements that might be search fields
            for element in self.page_state.elements:
                if element.tag == 'input':
                    # Check various attributes that indicate search field
                    if any(indicator in element.xpath.lower() for indicator in ['search', 'q', 'query']):
                        search_element = element
                        break
                    # Check if it's a text input without specific type
                    if 'type="text"' in element.xpath.lower() or 'name="q"' in element.xpath.lower():
                        search_element = element
                        break

            if search_element:
                self.add_message("system", f"Found search field at index {search_element.index}", {
                    "action": "search_field_found",
                    "index": search_element.index
                })

                # Perform search
                self.add_message("system", f"Searching Google for: {search_query}", {"action": "search", "query": search_query})

                success = await self.input_text(page, search_element.index, search_query)
                if success:
                    # Press Enter to search
                    await page.keyboard.press('Enter')
                    await page.wait_for_load_state("domcontentloaded")
                    await asyncio.sleep(3)

                    # Analyze search results
                    await self.analyze_page_state(page)

                    self.add_message("system", f"Search completed. Found {len(self.page_state.elements)} elements on results page", {
                        "action": "search_results",
                        "elements_count": len(self.page_state.elements)
                    })

                    # Show some search results
                    result_links = [e for e in self.page_state.elements if e.tag == 'a' and len(e.text.strip()) > 10]
                    for i, link in enumerate(result_links[:3]):
                        self.add_message("system", f"Result {i+1}: {link.text[:50]}", {
                            "action": "search_result",
                            "index": i,
                            "text": link.text[:50]
                        })

                    # Look for target site or click relevant results
                    if target_site:
                        return await self.click_search_result_for_site(page, target_site)
                    else:
                        return await self.click_first_relevant_result(page)
                else:
                    self.add_message("system", "Failed to input search query", {"action": "search_input_failed"})
                    return False
            else:
                self.add_message("system", "Could not find search input field on Google", {"action": "search_field_not_found"})

                # Try alternative approach - click on search area
                self.add_message("system", "Trying alternative search approach...", {"action": "alternative_search"})

                # Look for any clickable element that might open search
                clickable_elements = [e for e in self.page_state.elements if e.tag in ['div', 'span', 'input']]
                if clickable_elements:
                    # Try clicking the first few elements to see if search opens
                    for element in clickable_elements[:3]:
                        self.add_message("system", f"Trying to click element: {element.text[:30]}", {"action": "try_click"})
                        await self.click_element(page, element.index)
                        await asyncio.sleep(1)

                        # Check if search field appeared
                        await self.analyze_page_state(page)
                        search_inputs = [e for e in self.page_state.elements if e.tag == 'input']
                        if search_inputs:
                            self.add_message("system", "Search field appeared after click!", {"action": "search_field_appeared"})
                            return await self.search_with_google(page, search_query, target_site)

                return False

        except Exception as e:
            self.add_message("system", f"Error searching with Google: {str(e)}", {"action": "error"})
            return False

    async def search_with_duckduckgo(self, page: Page, search_query: str, target_site: str = None) -> bool:
        """Perform search using DuckDuckGo"""
        try:
            await page.goto("https://duckduckgo.com", wait_until="domcontentloaded")
            await asyncio.sleep(3)

            self.add_message("system", f"Searching DuckDuckGo for: {search_query}", {"action": "search", "query": search_query})

            search_input = await page.wait_for_selector('input[name="q"]', timeout=5000)
            await search_input.fill(search_query)
            await search_input.press('Enter')
            await page.wait_for_load_state("domcontentloaded")
            await asyncio.sleep(3)

            # Analyze results and click relevant links
            await self.analyze_page_state(page)

            if target_site:
                return await self.click_search_result_for_site(page, target_site)
            else:
                return await self.click_first_relevant_result(page)

        except Exception as e:
            self.add_message("system", f"Error searching with DuckDuckGo: {str(e)}", {"action": "error"})
            return False

    async def click_search_result_for_site(self, page: Page, target_site: str) -> bool:
        """Click on search result for specific site"""
        try:
            # Look for links containing the target site
            for element in self.page_state.elements:
                if element.tag == 'a' and target_site.lower() in element.text.lower():
                    self.add_message("system", f"Found link to {target_site}: {element.text[:50]}", {
                        "action": "target_found",
                        "site": target_site
                    })
                    await self.click_element(page, element.index)
                    await asyncio.sleep(3)
                    return True

            # Fallback: click first result
            return await self.click_first_relevant_result(page)

        except Exception as e:
            self.add_message("system", f"Error clicking search result: {str(e)}", {"action": "error"})
            return False

    async def click_first_relevant_result(self, page: Page) -> bool:
        """Click on the first relevant search result"""
        try:
            # Find the first clickable search result
            result_links = [e for e in self.page_state.elements
                          if e.tag == 'a' and len(e.text.strip()) > 10]

            if result_links:
                first_result = result_links[0]
                self.add_message("system", f"Clicking first search result: {first_result.text[:50]}", {
                    "action": "click_first_result"
                })
                await self.click_element(page, first_result.index)
                await asyncio.sleep(3)
                return True
            else:
                self.add_message("system", "No clickable search results found", {"action": "no_results"})
                return False

        except Exception as e:
            self.add_message("system", f"Error clicking first result: {str(e)}", {"action": "error"})
            return False


# Main execution
async def main():
    if len(sys.argv) != 3:
        print("Usage: python advanced_browser_runner.py <task_id> <task>")
        sys.exit(1)

    task_id = sys.argv[1]
    task = sys.argv[2]

    runner = AdvancedBrowserRunner(task_id, task)
    await runner.run_task()

if __name__ == "__main__":
    asyncio.run(main())
