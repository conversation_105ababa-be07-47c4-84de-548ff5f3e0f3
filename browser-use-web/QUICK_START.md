# 🚀 Guia de Início Rápido - Restrições de Domínio

## Pré-requisitos
- Node.js 18+ instalado
- Python 3.8+ instalado
- API key de um provedor LLM (OpenAI, Anthropic, ou Google)

## Instalação

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Verificar setup:**
   ```bash
   python3 test_setup.py
   ```

3. **Executar testes:**
   ```bash
   python3 test_domain_restrictions.py
   python3 test_integration.py
   ```

## Uso Básico

1. **Iniciar servidor:**
   ```bash
   npm run dev
   ```

2. **Acessar interface:**
   - Abra http://localhost:3000
   - Vá para aba "Advanced"
   - Adicione domínios permitidos

3. **Configurar tarefa:**
   - Configure sua API key
   - Defina domínios restritos
   - Execute sua tarefa

## Exemplo Prático

```bash
python3 example_restricted_task.py
```

## Solução de Problemas

- **Erro de dependências:** Execute `npm install`
- **Erro de Python:** Verifique se Python 3.8+ está instalado
- **Testes falhando:** Verifique se todos os arquivos estão presentes

## Suporte

Consulte DOMAIN_RESTRICTIONS.md para documentação completa.
