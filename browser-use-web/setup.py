#!/usr/bin/env python3
"""
Setup script for Browser-Use Web Interface
Installs and configures all necessary dependencies
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_node_version():
    """Check if Node.js is installed and compatible"""
    try:
        result = subprocess.run("node --version", shell=True, capture_output=True, text=True)
        version = result.stdout.strip()
        print(f"✅ Node.js {version} found")
        return True
    except:
        print("❌ Node.js not found. Please install Node.js 18 or higher")
        return False

def install_browser_use():
    """Install browser-use with all dependencies"""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install browser-use[cli]", "Installing browser-use with CLI support"),
        ("playwright install", "Installing Playwright browsers"),
        ("playwright install-deps", "Installing Playwright system dependencies")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True

def install_npm_dependencies():
    """Install Node.js dependencies"""
    if not run_command("npm install", "Installing Node.js dependencies"):
        return False
    return True

def create_env_file():
    """Create .env file with example configuration"""
    env_content = """# Browser-Use Web Interface Environment Variables

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Configuration  
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Google Configuration
GOOGLE_API_KEY=your-google-api-key-here

# Browser Configuration
BROWSER_USE_HEADLESS=false
BROWSER_USE_LOGGING_LEVEL=info

# Optional: GitHub token for MCP integration
GITHUB_TOKEN=your-github-token-here
"""
    
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, "w") as f:
            f.write(env_content)
        print("✅ Created .env file with example configuration")
        print("📝 Please edit .env file with your actual API keys")
    else:
        print("ℹ️ .env file already exists, skipping creation")

def main():
    """Main setup function"""
    print("🚀 Browser-Use Web Interface Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        sys.exit(1)
    
    # Install Python dependencies
    print("\n📦 Installing Python Dependencies")
    print("-" * 30)
    if not install_browser_use():
        print("❌ Failed to install Python dependencies")
        sys.exit(1)
    
    # Install Node.js dependencies
    print("\n📦 Installing Node.js Dependencies")
    print("-" * 30)
    if not install_npm_dependencies():
        print("❌ Failed to install Node.js dependencies")
        sys.exit(1)
    
    # Create environment file
    print("\n⚙️ Configuration")
    print("-" * 30)
    create_env_file()
    
    # Final instructions
    print("\n🎉 Setup Complete!")
    print("=" * 50)
    print("Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Run 'npm run dev' to start the development server")
    print("3. Open http://localhost:3000 in your browser")
    print("4. Configure your settings in the ⚙️ Config tab")
    print("\nFor help, see README.md or visit the documentation")

if __name__ == "__main__":
    main()