# 🛡️ Restrições de Domínio - Browser-Use Web

## Visão Geral

O sistema de restrições de domínio permite controlar quais sites o agente de IA pode visitar durante a automação do navegador. Este recurso é essencial para:

- **Segurança corporativa**: Impedir acesso a sites não autorizados
- **Compliance**: Atender políticas de segurança organizacionais
- **Testes controlados**: Limitar automação a ambientes específicos
- **Prevenção de vazamentos**: Evitar que dados sejam enviados para sites externos

## 🚀 Como Usar

### 1. Configuração pela Interface

1. Acesse a aba **"⚙️ Advanced"** na configuração
2. Na seção **"Allowed Domains"**:
   - Digite o domínio no campo de entrada (ex: `example.com`)
   - Pressione **Enter** ou clique em **"Add"**
   - Para remover, clique no **"×"** ao lado do domínio

### 2. Exemplos de Configuração

```typescript
// Configuração básica
{
  "allowed_domains": ["wikipedia.org", "github.com"]
}

// Configuração para ambiente corporativo
{
  "allowed_domains": [
    "company.com",
    "docs.company.com", 
    "internal.company.com"
  ]
}

// Configuração para pesquisa acadêmica
{
  "allowed_domains": [
    "scholar.google.com",
    "arxiv.org",
    "pubmed.ncbi.nlm.nih.gov"
  ]
}
```

## 🔧 Funcionamento Técnico

### Validação de Domínios

O sistema valida URLs usando as seguintes regras:

1. **Domínio exato**: `example.com` permite `https://example.com`
2. **Subdomínios**: `example.com` permite `https://sub.example.com`
3. **Prefixo www**: `example.com` permite `https://www.example.com`
4. **Protocolo**: Funciona com `http://` e `https://`

### Implementação

```python
def is_domain_allowed(url):
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    # Remove www. prefix
    if domain.startswith('www.'):
        domain = domain[4:]
    
    # Verifica se está na lista permitida
    for allowed_domain in allowed_domains:
        if domain == allowed_domain or domain.endswith('.' + allowed_domain):
            return True
    return False
```

## 📋 Casos de Uso

### 1. Pesquisa Acadêmica Restrita

```json
{
  "task": "Pesquisar sobre inteligência artificial",
  "config": {
    "allowed_domains": [
      "scholar.google.com",
      "arxiv.org",
      "ieee.org",
      "acm.org"
    ]
  }
}
```

### 2. Automação Corporativa

```json
{
  "task": "Coletar dados do sistema interno",
  "config": {
    "allowed_domains": [
      "intranet.company.com",
      "dashboard.company.com",
      "reports.company.com"
    ]
  }
}
```

### 3. Testes de E-commerce

```json
{
  "task": "Testar fluxo de compra",
  "config": {
    "allowed_domains": [
      "staging.shop.com",
      "test-payment.gateway.com"
    ]
  }
}
```

## ⚠️ Comportamento de Bloqueio

Quando o agente tenta acessar um domínio não autorizado:

1. **Bloqueio imediato**: A navegação é interrompida
2. **Log de segurança**: Evento é registrado nos logs
3. **Mensagem de erro**: `"Acesso negado: [URL] não está na lista de domínios permitidos"`
4. **Continuação da tarefa**: O agente tenta alternativas dentro dos domínios permitidos

## 🔍 Monitoramento e Logs

### Logs de Segurança

```
🚫 Domínio bloqueado: https://unauthorized-site.com
✅ Domínio permitido: https://wikipedia.org
📸 Página visitada: Wikipedia - Python (screenshot capturado)
```

### Dados de Auditoria

Cada execução gera logs detalhados incluindo:
- URLs visitadas
- Tentativas de acesso bloqueadas
- Screenshots das páginas permitidas
- Tempo de execução por domínio

## 🛠️ Configuração Avançada

### Sem Restrições

Para permitir acesso a qualquer site, deixe a lista vazia:

```json
{
  "allowed_domains": []
}
```

### Múltiplos Subdomínios

Para permitir todos os subdomínios de um domínio:

```json
{
  "allowed_domains": ["company.com"]
}
```

Isso permite:
- `https://company.com`
- `https://www.company.com`
- `https://api.company.com`
- `https://docs.company.com`
- `https://any.subdomain.company.com`

### Domínios Específicos

Para permitir apenas subdomínios específicos:

```json
{
  "allowed_domains": [
    "api.company.com",
    "docs.company.com"
  ]
}
```

## 🧪 Testes

### Teste Manual

1. Configure domínios restritos (ex: `["wikipedia.org"]`)
2. Execute uma tarefa que normalmente acessaria outros sites
3. Verifique nos logs se apenas Wikipedia foi acessada

### Teste Automatizado

```bash
cd browser-use-web
python3 test_domain_restrictions.py
python3 test_integration.py
```

## 📊 Estatísticas de Uso

O sistema coleta estatísticas sobre:
- Número de domínios configurados
- Tentativas de acesso bloqueadas
- Sites mais visitados dentro das restrições
- Tempo médio por domínio

## 🔐 Considerações de Segurança

### Boas Práticas

1. **Princípio do menor privilégio**: Configure apenas os domínios necessários
2. **Revisão regular**: Atualize a lista conforme necessário
3. **Monitoramento**: Revise logs regularmente
4. **Testes**: Valide configurações antes de usar em produção

### Limitações

- Não bloqueia redirecionamentos internos do site
- Não controla conteúdo dentro de iframes
- Não impede download de recursos externos (CSS, JS)

## 🆘 Solução de Problemas

### Problema: Tarefa falha por falta de informações

**Solução**: Adicione mais domínios relevantes à lista permitida

### Problema: Domínio não está sendo bloqueado

**Verificações**:
1. Confirme que o domínio não está na lista permitida
2. Verifique se não é um subdomínio de um domínio permitido
3. Revise os logs para confirmar a tentativa de acesso

### Problema: Subdomínio não está funcionando

**Solução**: Adicione o domínio principal (sem subdomínio) à lista

## 📞 Suporte

Para problemas ou dúvidas sobre restrições de domínio:

1. Verifique os logs de execução
2. Execute os testes automatizados
3. Revise esta documentação
4. Consulte os exemplos de configuração

---

**Versão**: 1.0  
**Última atualização**: Janeiro 2025  
**Compatibilidade**: Browser-Use Web v2.0+