// Debug script to generate and examine the Python script
const fs = require('fs');
const path = require('path');

// Import the script assembler
const { ScriptAssembler } = require('./src/lib/browser-use/script-assembler');

// Create a test task ID and task
const taskId = 'debug-task-id';
const task = 'Test task for debugging';

// Create a minimal config
const config = {
  task_type: 'research',
  provider: 'google',
  model: 'gemini-2.5-flash',
  api_key: 'test-api-key',
  temperature: 0,
  headless: false,
  use_vision: true,
  max_steps: 50,
  allowed_domains: ['example.com']
};

// Generate the Python script
const pythonScript = ScriptAssembler.assembleScript(taskId, task, config);

// Write the script to a file for examination
const scriptPath = path.join(__dirname, 'debug_script.py');
fs.writeFileSync(scriptPath, pythonScript);

console.log(`Debug script written to ${scriptPath}`);

// Extract lines around the problematic area (lines 325-335)
const lines = pythonScript.split('\n');
console.log('\nLines 325-335:');
for (let i = 324; i < 335; i++) {
  if (i < lines.length) {
    console.log(`${i+1}: ${lines[i]}`);
  }
}