#!/usr/bin/env python3
"""
Start script for Browser-Use Web Interface
Handles setup, testing, and launching the application
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path
import argparse

def run_command(command, description, capture_output=False):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
            return result.stdout.strip()
        else:
            subprocess.run(command, shell=True, check=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if capture_output and e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_dependencies():
    """Check if all dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    # Check Python dependencies
    try:
        import browser_use
        print("✅ browser-use is installed")
    except ImportError:
        print("❌ browser-use not found")
        return False
    
    # Check Node.js dependencies
    if not Path("node_modules").exists():
        print("❌ Node.js dependencies not installed")
        return False
    
    print("✅ All dependencies are installed")
    return True

def setup_if_needed():
    """Run setup if dependencies are missing"""
    if not check_dependencies():
        print("\n📦 Installing dependencies...")
        if not run_command("python3 setup.py", "Running setup"):
            print("❌ Setup failed. Please run 'python3 setup.py' manually")
            return False
    return True

def test_setup():
    """Run tests to verify setup"""
    print("\n🧪 Testing setup...")
    return run_command("python3 test_setup.py", "Running tests")

def start_development_server():
    """Start the Next.js development server"""
    print("\n🚀 Starting development server...")
    print("📝 The server will start at http://localhost:3000")
    print("🔄 Press Ctrl+C to stop the server")
    
    try:
        # Start the server
        process = subprocess.Popen(["npm", "run", "dev"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT,
                                 universal_newlines=True)
        
        # Wait a bit for server to start
        time.sleep(3)
        
        # Try to open browser
        try:
            webbrowser.open("http://localhost:3000")
            print("🌐 Opened browser at http://localhost:3000")
        except:
            print("🌐 Please open http://localhost:3000 in your browser")
        
        # Stream output
        for line in process.stdout:
            print(line.strip())
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")
        process.terminate()
        process.wait()
        print("✅ Server stopped")

def build_production():
    """Build for production"""
    print("🏗️ Building for production...")
    if run_command("npm run build", "Building application"):
        print("✅ Production build complete")
        print("🚀 Run 'npm start' to start the production server")
        return True
    return False

def show_help():
    """Show help information"""
    help_text = """
🚀 Browser-Use Web Interface Launcher

Usage: python3 start.py [command]

Commands:
  dev      Start development server (default)
  build    Build for production
  test     Run tests only
  setup    Run setup only
  help     Show this help

Examples:
  python3 start.py          # Start development server
  python3 start.py dev      # Same as above
  python3 start.py build    # Build for production
  python3 start.py test     # Run tests
  python3 start.py setup    # Run setup

Configuration:
  - Edit .env file for API keys
  - Use ⚙️ Config tab in web interface
  - See README.md for detailed instructions
"""
    print(help_text)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Browser-Use Web Interface Launcher")
    parser.add_argument("command", nargs="?", default="dev", 
                       choices=["dev", "build", "test", "setup", "help"],
                       help="Command to run")
    
    args = parser.parse_args()
    
    print("🌐 Browser-Use Web Interface")
    print("=" * 40)
    
    if args.command == "help":
        show_help()
        return
    
    if args.command == "setup":
        if not run_command("python3 setup.py", "Running setup"):
            sys.exit(1)
        return
    
    if args.command == "test":
        if not run_command("python3 test_setup.py", "Running tests"):
            sys.exit(1)
        return
    
    if args.command == "build":
        if not setup_if_needed():
            sys.exit(1)
        if not build_production():
            sys.exit(1)
        return
    
    if args.command == "dev":
        # Setup if needed
        if not setup_if_needed():
            sys.exit(1)
        
        # Run tests
        if not test_setup():
            print("⚠️ Tests failed, but continuing anyway...")
            print("   You may encounter issues. Run 'python3 start.py setup' to fix.")
        
        # Start development server
        start_development_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)