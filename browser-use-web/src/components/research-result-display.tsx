"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Search, TrendingUp, AlertTriangle, CheckCircle, ExternalLink, BookOpen, Star } from "lucide-react"

interface ResearchResultProps {
  content: string
  metadata?: {
    action?: string
    task_type?: string
    [key: string]: any
  }
}

interface TrendData {
  trends: string[]
  reception: string[]
}

export function ResearchResultDisplay({ content, metadata }: ResearchResultProps) {
  
  const parseVibecodingContent = (text: string): TrendData | null => {
    try {
      // Look for JSON content in the text
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonData = JSON.parse(jsonMatch[0]);
        if (jsonData.trends && jsonData.reception) {
          return {
            trends: jsonData.trends,
            reception: jsonData.reception
          };
        }
      }
      
      // Fallback: parse from structured text
      const trendsMatch = text.match(/\*\*Trends:\*\*\s*([\s\S]*?)(?=\n\n|\*\*|$)/);
      const receptionMatch = text.match(/\*\*Reception:\*\*\s*([\s\S]*?)(?=\n\n|\*\*|$)/);
      
      if (trendsMatch || receptionMatch) {
        const trends = trendsMatch ? trendsMatch[1].split('\n').filter(line => line.trim().startsWith('-')).map(line => line.replace(/^-\s*/, '').trim()) : [];
        const reception = receptionMatch ? receptionMatch[1].split('\n').filter(line => line.trim().startsWith('-')).map(line => line.replace(/^-\s*/, '').trim()) : [];
        
        return { trends, reception };
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing vibecoding content:', error);
      return null;
    }
  };

  const renderVibecodingResult = (data: TrendData) => {
    return (
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center pb-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Vibe Coding
            </h2>
          </div>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Análise completa das últimas tendências e receção do conceito de "Vibe Coding" 
            baseada em informações da Wikipedia
          </p>
        </div>

        {/* Trends Section */}
        {data.trends && data.trends.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-2xl font-semibold text-green-700 dark:text-green-300">
                📈 Tendências Principais
              </h3>
            </div>
            
            <div className="grid gap-4">
              {data.trends.map((trend, index) => (
                <div 
                  key={index} 
                  className="group p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border-l-4 border-green-500 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        {index + 1}
                      </div>
                    </div>
                    <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
                      {trend}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <Separator className="my-8" />

        {/* Reception Section */}
        {data.reception && data.reception.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <h3 className="text-2xl font-semibold text-orange-700 dark:text-orange-300">
                🎯 Receção e Críticas
              </h3>
            </div>
            
            <div className="grid gap-4">
              {data.reception.map((item, index) => {
                const isPositive = item.toLowerCase().includes('advocates') || item.toLowerCase().includes('allows') || item.toLowerCase().includes('suitable');
                const isNegative = item.toLowerCase().includes('critics') || item.toLowerCase().includes('concerns') || item.toLowerCase().includes('risky') || item.toLowerCase().includes('struggles');
                
                return (
                  <div 
                    key={index} 
                    className={`group p-4 rounded-lg border-l-4 hover:shadow-md transition-all duration-200 ${
                      isPositive 
                        ? 'bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-500' 
                        : isNegative 
                        ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-red-500'
                        : 'bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 border-gray-500'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {isPositive ? (
                          <CheckCircle className="h-5 w-5 text-blue-500" />
                        ) : isNegative ? (
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        ) : (
                          <BookOpen className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                      <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
                        {item}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <ExternalLink className="h-4 w-4" />
              <span>Fonte: Wikipedia - Vibe Coding</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Pesquisa realizada com sucesso
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderGenericResult = (text: string) => {
    // Remove technical metadata and debug information
    const cleanText = text
      .replace(/AgentHistoryList\(all_results=\[.*?\]\)/gs, '')
      .replace(/ActionResult\(.*?\)/gs, '')
      .replace(/Task completed successfully!/g, '')
      .replace(/🎯 Executing task:.*?\n/g, '')
      .replace(/\d{2}:\d{2}:\d{2}/g, '')
      .replace(/task_completed/g, '')
      .replace(/✅ Task completed successfully!/g, '')
      .trim();

    if (!cleanText) {
      return (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Nenhum conteúdo específico encontrado nos resultados.</p>
        </div>
      );
    }

    return (
      <div className="prose dark:prose-invert max-w-none">
        <div className="whitespace-pre-wrap text-gray-800 dark:text-gray-200 leading-relaxed">
          {cleanText}
        </div>
      </div>
    );
  };

  // Check if this is vibecoding content
  const vibecodingData = parseVibecodingContent(content);
  
  return (
    <Card className="w-full border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-900/10 dark:to-emerald-900/10">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-green-700 dark:text-green-300 flex items-center gap-2">
            <Search className="h-5 w-5 text-green-500" />
            Resultado da Pesquisa
          </CardTitle>
          {metadata?.action && (
            <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-300 dark:border-green-700">
              {metadata.action}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-green-200 dark:border-green-800 shadow-sm">
          {vibecodingData ? renderVibecodingResult(vibecodingData) : renderGenericResult(content)}
        </div>
      </CardContent>
    </Card>
  );
}
