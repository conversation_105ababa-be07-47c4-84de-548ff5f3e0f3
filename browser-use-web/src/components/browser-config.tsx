"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Save, RotateCcw, Eye, EyeOff, Globe } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface BrowserConfig {
  // LLM Configuration
  model: string;
  provider: "openai" | "anthropic" | "google";
  temperature: number;
  api_key: string;

  // Browser Configuration
  headless: boolean;
  use_vision: boolean;
  max_steps: number;
  window_width: number;
  window_height: number;

  // Task Configuration
  task_type: "research" | "form_filling" | "general";

  // Advanced Settings
  system_prompt: string;
  allowed_domains: string[];
  user_data_dir: string;
  disable_security: boolean;
}

const DEFAULT_CONFIG: BrowserConfig = {
  model: "gemini-2.5-flash",
  provider: "google",
  temperature: 0.0,
  api_key: "",
  headless: false,
  use_vision: true,
  max_steps: 50,
  window_width: 1280,
  window_height: 720,
  task_type: "research",
  system_prompt: "",
  allowed_domains: [],
  user_data_dir: "",
  disable_security: true,
};

const MODEL_OPTIONS = {
  openai: [
    { value: "gpt-4o", label: "GPT-4o (Recommended)" },
    { value: "gpt-4o-mini", label: "GPT-4o Mini" },
    { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
    { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
  ],
  anthropic: [
    {
      value: "claude-3-5-sonnet-20241022",
      label: "Claude 3.5 Sonnet (Recommended)",
    },
    { value: "claude-3-opus-20240229", label: "Claude 3 Opus" },
    { value: "claude-3-haiku-20240307", label: "Claude 3 Haiku" },
  ],
  google: [
    { value: "gemini-2.5-flash", label: "Gemini 2.5 Flash (Recommended)" },
    { value: "gemini-2.5-pro", label: "Gemini 2.5 Pro" },
    { value: "gemini-2.0-flash", label: "Gemini 2.0 Flash" },
    { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro" },
    { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash" },
  ],
};

interface BrowserConfigProps {
  onConfigChange?: (config: BrowserConfig) => void;
  initialConfig?: Partial<BrowserConfig>;
}

export function BrowserConfig({
  onConfigChange,
  initialConfig,
}: BrowserConfigProps) {
  const [config, setConfig] = useState<BrowserConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig,
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [isLoading, setSaving] = useState(false);

  useEffect(() => {
    // Load config from localStorage
    const savedConfig = localStorage.getItem("browser-use-config");
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig({ ...DEFAULT_CONFIG, ...parsed });
      } catch (error) {
        console.error("Error loading saved config:", error);
      }
    }
  }, []);

  const updateConfig = (updates: Partial<BrowserConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  };

  const saveConfig = async () => {
    setSaving(true);
    try {
      localStorage.setItem("browser-use-config", JSON.stringify(config));
      toast.success("Configuration saved successfully!");
    } catch (error) {
      console.error("Error saving config:", error);
      toast.error("Failed to save configuration");
    } finally {
      setSaving(false);
    }
  };

  const resetConfig = () => {
    setConfig(DEFAULT_CONFIG);
    localStorage.removeItem("browser-use-config");
    toast.success("Configuration reset to defaults");
  };

  const addDomain = (domain: string) => {
    if (domain && !config.allowed_domains.includes(domain)) {
      updateConfig({
        allowed_domains: [...config.allowed_domains, domain],
      });
    }
  };

  const removeDomain = (domain: string) => {
    updateConfig({
      allowed_domains: config.allowed_domains.filter((d) => d !== domain),
    });
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case "openai":
        return "🤖";
      case "anthropic":
        return "🧠";
      case "google":
        return "🔍";
      default:
        return "⚡";
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="h-6 w-6 text-blue-500" />
          <div>
            <h2 className="text-2xl font-bold">Browser-Use Configuration</h2>
            <p className="text-muted-foreground">
              Configure your AI browser automation settings
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetConfig}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={saveConfig} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="llm" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="llm">🤖 LLM</TabsTrigger>
          <TabsTrigger value="browser">🌐 Browser</TabsTrigger>
          <TabsTrigger value="agent">🎯 Agent</TabsTrigger>
          <TabsTrigger value="advanced">⚙️ Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="llm" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getProviderIcon(config.provider)} Language Model Configuration
              </CardTitle>
              <CardDescription>
                Configure the AI model that will control the browser
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">Provider</Label>
                  <Select
                    value={config.provider}
                    onValueChange={(value: string) => {
                      const provider = value as
                        | "openai"
                        | "anthropic"
                        | "google";
                      updateConfig({
                        provider: provider,
                        model: MODEL_OPTIONS[provider][0].value,
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">🤖 OpenAI</SelectItem>
                      <SelectItem value="anthropic">🧠 Anthropic</SelectItem>
                      <SelectItem value="google">🔍 Google</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={config.model}
                    onValueChange={(value: string) =>
                      updateConfig({ model: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MODEL_OPTIONS[config.provider].map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="api_key">API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="api_key"
                    type={showApiKey ? "text" : "password"}
                    value={config.api_key}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      updateConfig({ api_key: e.target.value })
                    }
                    placeholder={`Enter your ${config.provider} API key`}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="temperature">
                  Temperature: {config.temperature}
                </Label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={config.temperature}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    updateConfig({ temperature: parseFloat(e.target.value) })
                  }
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>More Focused</span>
                  <span>More Creative</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="browser" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Browser Configuration
              </CardTitle>
              <CardDescription>
                Configure how the browser behaves during automation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="window_width">Window Width</Label>
                  <Input
                    id="window_width"
                    type="number"
                    value={config.window_width}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      updateConfig({ window_width: parseInt(e.target.value) })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="window_height">Window Height</Label>
                  <Input
                    id="window_height"
                    type="number"
                    value={config.window_height}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      updateConfig({ window_height: parseInt(e.target.value) })
                    }
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Headless Mode</Label>
                  <div className="text-sm text-muted-foreground">
                    Run browser without visible window (faster but no visual
                    feedback)
                  </div>
                </div>
                <Switch
                  checked={config.headless}
                  onCheckedChange={(checked: boolean) =>
                    updateConfig({ headless: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Disable Security</Label>
                  <div className="text-sm text-muted-foreground">
                    Disable browser security features for better automation
                  </div>
                </div>
                <Switch
                  checked={config.disable_security}
                  onCheckedChange={(checked: boolean) =>
                    updateConfig({ disable_security: checked })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="user_data_dir">
                  User Data Directory (Optional)
                </Label>
                <Input
                  id="user_data_dir"
                  value={config.user_data_dir}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    updateConfig({ user_data_dir: e.target.value })
                  }
                  placeholder="Path to Chrome user data directory"
                />
                <div className="text-xs text-muted-foreground">
                  Use existing Chrome profile for cookies, extensions, etc.
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎯 Agent Configuration
              </CardTitle>
              <CardDescription>
                Configure the AI agent's behavior and capabilities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="task_type">Task Type</Label>
                <Select
                  value={config.task_type}
                  onValueChange={(value: "research" | "form_filling" | "general") =>
                    updateConfig({ task_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="research">🔍 Research (Web Search & Content Extraction)</SelectItem>
                    <SelectItem value="form_filling">📝 Form Filling (Automated Form Completion)</SelectItem>
                    <SelectItem value="general">🌐 General (Basic Web Navigation)</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground">
                  Select the type of task to optimize the agent's behavior
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max_steps">
                  Maximum Steps: {config.max_steps}
                </Label>
                <input
                  type="range"
                  min="10"
                  max="200"
                  step="10"
                  value={config.max_steps}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    updateConfig({ max_steps: parseInt(e.target.value) })
                  }
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Quick Tasks</span>
                  <span>Complex Tasks</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Use Vision</Label>
                  <div className="text-sm text-muted-foreground">
                    Enable AI to see and analyze page screenshots
                  </div>
                </div>
                <Switch
                  checked={config.use_vision}
                  onCheckedChange={(checked: boolean) =>
                    updateConfig({ use_vision: checked })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="system_prompt">
                  Custom System Prompt (Optional)
                </Label>
                <Textarea
                  id="system_prompt"
                  value={config.system_prompt}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    updateConfig({ system_prompt: e.target.value })
                  }
                  placeholder="Add custom instructions for the AI agent..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>⚙️ Advanced Settings</CardTitle>
              <CardDescription>
                Advanced configuration options for power users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Allowed Domains</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add domain (e.g., example.com)"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addDomain(e.currentTarget.value);
                        e.currentTarget.value = "";
                      }
                    }}
                  />
                  <Button
                    variant="outline"
                    onClick={(e) => {
                      const input = e.currentTarget
                        .previousElementSibling as HTMLInputElement;
                      addDomain(input.value);
                      input.value = "";
                    }}
                  >
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.allowed_domains.map((domain) => (
                    <Badge
                      key={domain}
                      variant="secondary"
                      className="cursor-pointer"
                    >
                      {domain}
                      <button
                        onClick={() => removeDomain(domain)}
                        className="ml-2 hover:text-red-500"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="text-xs text-muted-foreground">
                  Restrict browser to only visit these domains (leave empty for
                  no restrictions). Domain restrictions enhance security by preventing
                  the AI from accessing unauthorized websites during automation tasks.
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
