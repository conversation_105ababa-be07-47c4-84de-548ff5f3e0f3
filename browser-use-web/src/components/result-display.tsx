"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, FileText, Search, Globe } from "lucide-react"

interface ResultDisplayProps {
  content: string
  metadata?: {
    action?: string
    [key: string]: any
  }
}

export function ResultDisplay({ content, metadata }: ResultDisplayProps) {
  // Parse structured content if it looks like markdown or structured data
  const parseContent = (text: string) => {
    // Determine the task type from metadata
    const taskType = metadata?.task_type || 'research';
    
    // For form filling tasks, show a success message with form details
    if (taskType === 'form_filling' && metadata?.form_data) {
      return parseFormFillingResult(text, metadata);
    }
    
    // Check if it's structured content with sections
    if (text.includes('# ') || text.includes('## ') || text.includes('### ')) {
      return parseMarkdownContent(text)
    }
    
    // Check if it's JSON-like content
    if (text.includes('"definition":') || text.includes('"benefits":')) {
      return parseStructuredData(text)
    }
    
    // Regular text
    return <div className="prose dark:prose-invert max-w-none">{text}</div>
  }
  
  const parseFormFillingResult = (text: string, metadata: any) => {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-500">
          <h3 className="text-lg font-semibold text-green-700 dark:text-green-300">
            Formulário preenchido com sucesso
          </h3>
          <p className="text-sm text-green-600 dark:text-green-400 mt-1">
            {metadata.fields_count || 'Vários'} campos foram preenchidos automaticamente
          </p>
        </div>
        
        {metadata.form_data && (
          <div className="mt-4">
            <h4 className="text-md font-medium mb-2">Dados preenchidos:</h4>
            <div className="grid gap-2">
              {Object.entries(metadata.form_data).map(([field, value]: [string, any]) => (
                <div key={field} className="flex items-start gap-2 p-2 bg-gray-50 dark:bg-gray-800/50 rounded">
                  <span className="font-medium text-gray-700 dark:text-gray-300">{field}:</span>
                  <span className="text-gray-600 dark:text-gray-400">{value}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
          {text}
        </div>
      </div>
    );
  }

  const parseMarkdownContent = (text: string) => {
    const lines = text.split('\n')
    const elements: JSX.Element[] = []
    
    lines.forEach((line, index) => {
      if (line.startsWith('# ')) {
        elements.push(
          <h1 key={index} className="text-2xl font-bold mb-4 text-blue-600 dark:text-blue-400">
            {line.substring(2)}
          </h1>
        )
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={index} className="text-xl font-semibold mb-3 text-green-600 dark:text-green-400 mt-6">
            {line.substring(3)}
          </h2>
        )
      } else if (line.startsWith('### ')) {
        elements.push(
          <h3 key={index} className="text-lg font-medium mb-2 text-purple-600 dark:text-purple-400 mt-4">
            {line.substring(4)}
          </h3>
        )
      } else if (line.startsWith('- ')) {
        elements.push(
          <div key={index} className="flex items-start gap-2 mb-2 ml-4">
            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span className="text-gray-700 dark:text-gray-300">{line.substring(2)}</span>
          </div>
        )
      } else if (line.trim() && !line.startsWith('#')) {
        elements.push(
          <p key={index} className="mb-3 text-gray-800 dark:text-gray-200 leading-relaxed">
            {line}
          </p>
        )
      }
    })
    
    return <div className="space-y-2">{elements}</div>
  }

  const parseStructuredData = (text: string) => {
    try {
      // Extract JSON-like content
      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const jsonStr = jsonMatch[0]
        const data = JSON.parse(jsonStr)
        
        return (
          <div className="space-y-6">
            {data.extracted_information?.definition && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-blue-600 dark:text-blue-400 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Definição
                </h3>
                <div className="space-y-2">
                  {data.extracted_information.definition.map((def: string, index: number) => (
                    <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                      <p className="text-gray-800 dark:text-gray-200">{def}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {data.extracted_information?.benefits && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-green-600 dark:text-green-400 flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Benefícios
                </h3>
                <div className="grid gap-2">
                  {data.extracted_information.benefits.map((benefit: string, index: number) => (
                    <div key={index} className="flex items-start gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-800 dark:text-gray-200">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {data.extracted_information?.risks && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-red-600 dark:text-red-400 flex items-center gap-2">
                  ⚠️ Riscos
                </h3>
                <div className="space-y-2">
                  {data.extracted_information.risks.map((risk: string, index: number) => (
                    <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
                      <p className="text-gray-800 dark:text-gray-200">{risk}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )
      }
    } catch (error) {
      // Fallback to regular parsing
    }
    
    return parseMarkdownContent(text)
  }

  return (
    <Card className="w-full border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/10">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-green-700 dark:text-green-300 flex items-center gap-2">
            {metadata?.task_type === 'form_filling' ? 
              <CheckCircle className="h-5 w-5 text-blue-500" /> : 
             metadata?.task_type === 'general' ? 
              <Globe className="h-5 w-5 text-purple-500" /> : 
              <Search className="h-5 w-5 text-green-500" />
            }
            {metadata?.task_type === 'form_filling' ? 'Formulário Preenchido' : 
             metadata?.task_type === 'general' ? 'Navegação Web' : 'Resultado da Pesquisa'}
          </CardTitle>
          {metadata?.action && (
            <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-300 dark:border-green-700">
              {metadata.action}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-green-200 dark:border-green-800">
          {parseContent(content)}
        </div>
      </CardContent>
    </Card>
  )
}