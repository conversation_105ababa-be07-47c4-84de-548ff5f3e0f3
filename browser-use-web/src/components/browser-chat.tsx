"use client"

import { useState, useR<PERSON>, useEffect } from "react"
import { Send, Bot, User, Globe, Loader2, Square, Monitor, Eye, Settings, History, Camera, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Toaster } from "@/components/ui/sonner"
import { toast } from "sonner"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BrowserConfig } from "@/components/browser-config"
import { ResultDisplay } from "@/components/result-display"

interface Message {
  id: string
  type: "user" | "assistant" | "system" | "result"
  content: string
  timestamp: string
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
    step?: number
    element_count?: number
    page_analysis?: any
    [key: string]: any
  }
}

interface BrowserTask {
  id: string
  task: string
  status: "pending" | "running" | "completed" | "failed"
  messages: Message[]
  createdAt: string
  duration?: number
  config?: BrowserTaskConfig
  screenshots?: string[]
  currentUrl?: string
  pageTitle?: string
}

interface BrowserTaskConfig {
  model: string
  provider: 'openai' | 'anthropic' | 'google'
  temperature: number
  headless: boolean
  use_vision: boolean
  max_steps: number
  api_key?: string
  task_type?: 'research' | 'form_filling' | 'general'
}

export function BrowserChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isRunning, setIsRunning] = useState(false)
  const [currentTask, setCurrentTask] = useState<BrowserTask | null>(null)
  const [activeTab, setActiveTab] = useState("chat")
  const [config, setConfig] = useState<BrowserTaskConfig>({
    model: 'gpt-4o',
    provider: 'openai',
    temperature: 0.0,
    headless: false,
    use_vision: true,
    max_steps: 50,
    api_key: '',
    task_type: 'research'
  })
  const [taskHistory, setTaskHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [currentScreenshot, setCurrentScreenshot] = useState<string>("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  useEffect(() => {
    // Load task history from localStorage
    const savedHistory = localStorage.getItem('browser-use-history')
    if (savedHistory) {
      try {
        setTaskHistory(JSON.parse(savedHistory))
      } catch (error) {
        console.error('Error loading task history:', error)
      }
    }

    // Load config from localStorage
    const savedConfig = localStorage.getItem('browser-use-config')
    if (savedConfig) {
      try {
        setConfig(JSON.parse(savedConfig))
      } catch (error) {
        console.error('Error loading config:', error)
      }
    }
  }, [])

  useEffect(() => {
    // Update current screenshot when task changes
    if (currentTask?.screenshots && currentTask.screenshots.length > 0) {
      setCurrentScreenshot(currentTask.screenshots[currentTask.screenshots.length - 1])
    }
  }, [currentTask])

  const sendMessage = async () => {
    if (!input.trim() || isRunning) return

    // Validate configuration
    if (!config.api_key) {
      toast.error("Please configure your API key in settings first")
      setActiveTab("config")
      return
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: input,
      timestamp: new Date().toISOString(),
    }

    setMessages(prev => [...prev, userMessage])
    
    // Add to history
    const newHistory = [input, ...taskHistory.filter(h => h !== input)].slice(0, 50)
    setTaskHistory(newHistory)
    localStorage.setItem('browser-use-history', JSON.stringify(newHistory))
    setHistoryIndex(-1)
    
    const currentInput = input
    setInput("")
    setIsRunning(true)

    try {
      const response = await fetch("/api/browser-use", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          task: currentInput,
          config: config,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to start enhanced browser task")
      }

      const data = await response.json()
      setCurrentTask(data.task)
      setMessages(data.task.messages)

      pollTaskStatus(data.task.id)
    } catch (error) {
      console.error("Failed to start browser task:", error)
      toast.error("Failed to start browser task")
      setIsRunning(false)
    }
  }

  const pollTaskStatus = async (taskId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/browser-use?id=${taskId}`)
        const data = await response.json()

        if (data.task) {
          setCurrentTask(data.task)
          setMessages(data.task.messages)
          
          // Update screenshot if available
          if (data.task.screenshots && data.task.screenshots.length > 0) {
            setCurrentScreenshot(data.task.screenshots[data.task.screenshots.length - 1])
          }
        }

        if (data.task?.status === "completed" || data.task?.status === "failed") {
          setIsRunning(false)
          clearInterval(interval)
          
          if (data.task.status === "completed") {
            toast.success("Task completed successfully!")
          } else {
            toast.error("Task failed to complete")
          }
        }
      } catch (_error) {
        console.error("Error polling task status:", _error)
        setIsRunning(false)
        clearInterval(interval)
        toast.error("Error checking task status")
      }
    }, 1000) // Poll more frequently for better real-time updates

    return () => clearInterval(interval)
  }

  const stopTask = async () => {
    if (!currentTask) return

    try {
      await fetch(`/api/browser-use?id=${currentTask.id}`, {
        method: "DELETE",
      })
      setIsRunning(false)
      toast.success("Browser task has been stopped")
    } catch (error) {
      console.error("Failed to stop task:", error)
      toast.error("Failed to stop task")
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    } else if (e.key === "ArrowUp" && !e.shiftKey && input === "" && taskHistory.length > 0) {
      e.preventDefault()
      const newIndex = Math.min(historyIndex + 1, taskHistory.length - 1)
      setHistoryIndex(newIndex)
      setInput(taskHistory[newIndex])
    } else if (e.key === "ArrowDown" && !e.shiftKey && historyIndex >= 0) {
      e.preventDefault()
      const newIndex = historyIndex - 1
      setHistoryIndex(newIndex)
      setInput(newIndex >= 0 ? taskHistory[newIndex] : "")
    }
  }

  const getMessageIcon = (message: Message) => {
    if (message.type === "user") return <User className="h-4 w-4" />
    if (message.type === "assistant") return <Bot className="h-4 w-4 text-blue-500" />
    if (message.type === "result") return <Zap className="h-4 w-4 text-green-500" />
    if (message.metadata?.action === "screenshot") return <Camera className="h-4 w-4 text-purple-500" />
    return <Monitor className="h-4 w-4 text-gray-500" />
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'running': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <Toaster />
      <div className="flex flex-col h-full max-w-4xl mx-auto p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
            <TabsTrigger value="chat" className="dark:text-gray-200 dark:data-[state=active]:bg-gray-600 dark:data-[state=active]:text-white">💬 Chat</TabsTrigger>
            <TabsTrigger value="browser" className="dark:text-gray-200 dark:data-[state=active]:bg-gray-600 dark:data-[state=active]:text-white">🖥️ Browser</TabsTrigger>
            <TabsTrigger value="config" className="dark:text-gray-200 dark:data-[state=active]:bg-gray-600 dark:data-[state=active]:text-white">⚙️ Config</TabsTrigger>
            <TabsTrigger value="history" className="dark:text-gray-200 dark:data-[state=active]:bg-gray-600 dark:data-[state=active]:text-white">📜 History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="chat" className="mt-0">
            <Card className="flex-1 flex flex-col h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-sm overflow-hidden">
              <div className="p-4 border-b flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-blue-500" />
                    <h2 className="text-lg font-semibold">Browser AI Assistant</h2>
                  </div>
                  <div className="flex items-center gap-2">
                    <a
                      href="/mcp"
                      className="text-sm bg-purple-100 hover:bg-purple-200 text-purple-700 px-3 py-1 rounded-md transition-colors flex items-center gap-1"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Try MCP
                    </a>
                    {isRunning && (
                      <>
                        <Badge variant="secondary" className="animate-pulse">
                          <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          Running
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={stopTask}
                          className="h-8"
                        >
                          <Square className="h-3 w-3 mr-1" />
                          Stop
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <ScrollArea className="flex-1 p-4 overflow-y-auto" ref={scrollAreaRef}>
                <div className="space-y-4 w-full max-w-full overflow-hidden">
                    {messages.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">Ask me to do anything on the web...</p>
                        <p className="text-sm mt-2 text-muted-foreground">
                          Examples: &quot;Find the latest news about AI&quot;, &quot;Book a flight to Paris&quot;, &quot;Check my email&quot;
                        </p>
                      </div>
                    ) : (
                      messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 w-full ${message.type === "user" ? "justify-end" : "justify-start"}`}
                      >
                        {message.type !== "user" && (
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            {getMessageIcon(message)}
                          </div>
                        )}
{message.type === "result" ? (
                          <div className="max-w-[85%] w-full">
                            <ResultDisplay 
                              content={message.content} 
                              metadata={{
                                ...message.metadata,
                                task_type: config.task_type || 'research',
                                action: message.metadata?.action || (
                                  config.task_type === 'form_filling' ? "Preenchimento de Formulário" :
                                  config.task_type === 'general' ? "Navegação Web" : "Resultado da Pesquisa"
                                )
                              }}
                            />
                            <p className="text-xs opacity-50 mt-2 text-right">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </p>
                          </div>
                        ) : (
                          <div
                            className={`max-w-[70%] break-words rounded-lg px-4 py-2 ${
                              message.type === "user"
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted text-foreground dark:bg-gray-800 dark:text-gray-100"
                            }`}
                          >
                            <div className="flex items-center gap-2 mb-1">
                              {message.metadata?.action && (
                                <Badge variant="outline" className="text-xs">
                                  {message.metadata.action}
                                </Badge>
                              )}
                              {message.metadata?.step && (
                                <Badge variant="secondary" className="text-xs">
                                  Step {message.metadata.step}
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                            {message.metadata?.url && (
                              <p className="text-xs opacity-70 mt-1">
                                <a 
                                  href={message.metadata.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="hover:underline"
                                >
                                  🔗 {new URL(message.metadata.url).hostname}
                                </a>
                              </p>
                            )}
                            {message.metadata?.screenshot && (
                              <div className="mt-2">
                                <img
                                  src={`data:image/png;base64,${message.metadata.screenshot}`}
                                  alt="Screenshot"
                                  className="max-w-full h-auto rounded border cursor-pointer"
                                  onClick={() => setCurrentScreenshot(message.metadata!.screenshot!)}
                                />
                              </div>
                            )}
                            <p className="text-xs opacity-50 mt-1">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </p>
                          </div>
                        )}
                        {message.type === "user" && (
                          <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>

              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <Textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask me to do something on the web... (↑/↓ for history)"
                    className="min-h-[40px] max-h-[120px]"
                    disabled={isRunning}
                  />
                  <Button
                    size="icon"
                    onClick={sendMessage}
                    disabled={!input.trim() || isRunning}
                    className="h-10 w-10"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="browser" className="mt-0">
            <Card className="h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-sm">
              {currentScreenshot ? (
                <div className="h-full flex flex-col">
                  <div className="p-4 border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Camera className="h-5 w-5 text-purple-500" />
                        <h3 className="font-semibold">Live Browser View</h3>
                      </div>
                      <div className="flex items-center gap-2">
                        {currentTask?.status && (
                          <Badge className={getStatusColor(currentTask.status)}>
                            {currentTask.status.toUpperCase()}
                          </Badge>
                        )}
                        {currentTask?.currentUrl && (
                          <Badge variant="outline" className="text-xs">
                            {new URL(currentTask.currentUrl).hostname}
                          </Badge>
                        )}
                      </div>
                    </div>
                    {currentTask?.pageTitle && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {currentTask.pageTitle}
                      </p>
                    )}
                  </div>
                  <div className="flex-1 p-4 overflow-auto">
                    <img
                      src={`data:image/png;base64,${currentScreenshot}`}
                      alt="Browser Screenshot"
                      className="w-full h-auto rounded-lg border shadow-sm"
                    />
                  </div>
                </div>
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">Browser View</p>
                    <p className="text-sm mt-2">
                      {isRunning 
                        ? "Waiting for browser screenshots..." 
                        : "Start a task to see live browser screenshots"
                      }
                    </p>
                  </div>
                </div>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="config" className="mt-0">
            <Card className="h-[600px] overflow-auto">
              <BrowserConfig 
                onConfigChange={setConfig}
                initialConfig={config}
              />
            </Card>
          </TabsContent>

          <TabsContent value="history" className="mt-0">
            <Card className="h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-sm">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <History className="h-5 w-5 text-green-500" />
                    <h3 className="font-semibold">Task History</h3>
                  </div>
                  <Badge variant="outline">
                    {taskHistory.length} tasks
                  </Badge>
                </div>
              </div>
              <ScrollArea className="flex-1 p-4">
                {taskHistory.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">No task history yet</p>
                    <p className="text-sm mt-2">
                      Your previous tasks will appear here
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {taskHistory.map((task, index) => (
                      <div
                        key={index}
                        className="p-3 rounded-lg border bg-muted/50 hover:bg-muted cursor-pointer transition-colors"
                        onClick={() => {
                          setInput(task)
                          setActiveTab("chat")
                        }}
                      >
                        <p className="text-sm font-medium">{task}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Click to reuse this task
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
