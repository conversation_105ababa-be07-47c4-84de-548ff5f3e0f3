import { NextRequest, NextResponse } from 'next/server'
import { getTask, setTask } from '@/lib/task-storage'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: taskId } = await params

  if (!taskId) {
    return NextResponse.json(
      { error: 'Task ID is required' },
      { status: 400 }
    )
  }

  const task = getTask(taskId)
  if (!task) {
    return NextResponse.json(
      { error: 'Task not found' },
      { status: 404 }
    )
  }

  if (task.status === 'running') {
    task.status = 'failed'
    task.duration = Date.now() - new Date(task.createdAt).getTime()
    task.messages.push({
      id: crypto.randomUUID(),
      type: 'system',
      content: 'Task was stopped by user',
      timestamp: new Date().toISOString(),
    })
    setTask(taskId, task)
  }

  return NextResponse.json({ success: true })
}
