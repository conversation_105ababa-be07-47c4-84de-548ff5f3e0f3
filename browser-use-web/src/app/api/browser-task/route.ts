import { NextRequest, NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'
import { tasks, setTask } from '@/lib/task-storage'
import { spawn } from 'child_process'
import path from 'path'
import fs from 'fs'

interface BrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: Message[]
  createdAt: string
  duration?: number
}

interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const { task, model } = await request.json()

    if (!task || typeof task !== 'string') {
      return NextResponse.json(
        { error: 'Task is required' },
        { status: 400 }
      )
    }

    const taskId = uuidv4()
    const newTask: BrowserTask = {
      id: taskId,
      task,
      status: 'pending',
      messages: [
        {
          id: uuidv4(),
          type: 'system',
          content: `Starting browser task: ${task}`,
          timestamp: new Date().toISOString(),
        }
      ],
      createdAt: new Date().toISOString(),
    }

    setTask(taskId, newTask)

    // Start the browser task in the background
    startBrowserTask(taskId, task, model)

    return NextResponse.json({ task: newTask })
  } catch (error) {
    console.error('Error creating browser task:', error)
    return NextResponse.json(
      { error: 'Failed to create browser task' },
      { status: 500 }
    )
  }
}

async function startBrowserTask(taskId: string, task: string, model: string) {
  const taskData = tasks.get(taskId)
  if (!taskData) return

  // Update status to running
  taskData.status = 'running'
  taskData.messages.push({
    id: uuidv4(),
    type: 'assistant',
    content: 'Starting browser automation...',
    timestamp: new Date().toISOString(),
  })

  try {
    // Use the advanced Python script to run the browser task
    const scriptPath = path.join(process.cwd(), 'advanced_browser_runner.py')
    const pythonProcess = spawn('python3', [scriptPath, taskId, task])

    pythonProcess.stdout.on('data', (data: Buffer) => {
      const output = data.toString()
      const lines = output.split('\n')

      for (const line of lines) {
        if (line.startsWith('PROGRESS:')) {
          try {
            const progressData = JSON.parse(line.substring(9))

            // Update task with new messages
            if (progressData.messages) {
              taskData.messages = progressData.messages.map((msg: any) => ({
                id: msg.id,
                type: msg.type,
                content: msg.content,
                timestamp: new Date(msg.timestamp).toISOString(),
                metadata: msg.metadata
              }))
            }

            // Update status
            if (progressData.status) {
              taskData.status = progressData.status
            }

            // Set duration if completed
            if (progressData.status === 'completed' || progressData.status === 'failed') {
              taskData.duration = Date.now() - new Date(taskData.createdAt).getTime()
            }

          } catch (parseError) {
            console.error('Error parsing progress data:', parseError)
          }
        }
      }
    })

    pythonProcess.stderr.on('data', (data: Buffer) => {
      console.error('Python script error:', data.toString())
    })

    pythonProcess.on('close', (code: number) => {
      if (code !== 0 && taskData.status === 'running') {
        taskData.status = 'failed'
        taskData.messages.push({
          id: uuidv4(),
          type: 'assistant',
          content: 'Sorry, I encountered an error while completing this task.',
          timestamp: new Date().toISOString(),
        })
      }
    })

  } catch (error) {
    console.error('Error in browser task:', error)
    taskData.status = 'failed'
    taskData.messages.push({
      id: uuidv4(),
      type: 'assistant',
      content: 'Sorry, I encountered an error while completing this task.',
      timestamp: new Date().toISOString(),
    })
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const taskId = searchParams.get('taskId')

  if (!taskId) {
    return NextResponse.json(
      { error: 'Task ID is required' },
      { status: 400 }
    )
  }

  const task = tasks.get(taskId)
  if (!task) {
    return NextResponse.json(
      { error: 'Task not found' },
      { status: 404 }
    )
  }

  return NextResponse.json(task)
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const taskId = searchParams.get('taskId')

  if (!taskId) {
    return NextResponse.json(
      { error: 'Task ID is required' },
      { status: 400 }
    )
  }

  const task = tasks.get(taskId)
  if (!task) {
    return NextResponse.json(
      { error: 'Task not found' },
      { status: 404 }
    )
  }

  try {
    // In a real implementation, this would stop the browser task
    // For now, we'll just remove the task from storage
    tasks.delete(taskId)
    return NextResponse.json({ message: 'Task deleted successfully' })
  } catch (error) {
    console.error("Failed to stop task:", error)
    return NextResponse.json(
      { error: 'Failed to stop task' },
      { status: 500 }
    )
  }
}
