import { NextRequest, NextResponse } from 'next/server'
import { spawn } from 'child_process'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'

// In-memory storage for MCP tasks
const mcpTasks = new Map<string, MCPBrowserTask>()

interface MCPBrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: MCPTaskMessage[]
  createdAt: string
  duration?: number
  mcpServers?: string[]
}

interface MCPTaskMessage {
  id: string
  type: 'system' | 'assistant' | 'user'
  content: string
  timestamp: string
  metadata?: {
    action?: string
    mcp_servers?: number
    servers_connected?: number
    [key: string]: any
  }
}

export async function POST(request: NextRequest) {
  try {
    const { task, geminiApiKey } = await request.json()

    if (!task || typeof task !== 'string') {
      return NextResponse.json(
        { error: 'Task is required' },
        { status: 400 }
      )
    }

    const taskId = uuidv4()
    const newTask: MCPBrowserTask = {
      id: taskId,
      task,
      status: 'pending',
      messages: [
        {
          id: uuidv4(),
          type: 'system',
          content: `Starting MCP-enabled browser task: ${task}`,
          timestamp: new Date().toISOString(),
        }
      ],
      createdAt: new Date().toISOString(),
    }

    mcpTasks.set(taskId, newTask)

    // Start the MCP browser task in the background
    startMCPBrowserTask(taskId, task, geminiApiKey)

    return NextResponse.json({ task: newTask })
  } catch (error) {
    console.error('Error creating MCP browser task:', error)
    return NextResponse.json(
      { error: 'Failed to create MCP browser task' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const taskId = searchParams.get('id')

  if (taskId) {
    const task = mcpTasks.get(taskId)
    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }
    return NextResponse.json({ task })
  }

  // Return all tasks
  const allTasks = Array.from(mcpTasks.values())
  return NextResponse.json({ tasks: allTasks })
}

async function startMCPBrowserTask(taskId: string, task: string, geminiApiKey?: string) {
  const taskData = mcpTasks.get(taskId)
  if (!taskData) return

  // Update status to running
  taskData.status = 'running'
  taskData.messages.push({
    id: uuidv4(),
    type: 'assistant',
    content: 'Starting MCP-enabled browser automation with Gemini...',
    timestamp: new Date().toISOString(),
  })

  try {
    // Use the Hybrid MCP + Real Browser Python script
    const scriptPath = path.join(process.cwd(), 'hybrid_mcp_browser_runner.py')
    
    // Set up environment variables
    const env = { ...process.env }
    if (geminiApiKey) {
      env.GOOGLE_API_KEY = geminiApiKey
    }

    const pythonProcess = spawn('/Volumes/Samsung990Pro/miniconda3/envs/BrowserUse/bin/python', [scriptPath, taskId, task], {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    })

    pythonProcess.stdout.on('data', (data: Buffer) => {
      const output = data.toString()
      const lines = output.split('\n')

      for (const line of lines) {
        if (line.startsWith('PROGRESS:')) {
          try {
            const progressData = JSON.parse(line.substring(9))

            // Update task with new messages
            if (progressData.messages) {
              taskData.messages = progressData.messages.map((msg: any) => ({
                id: msg.id,
                type: msg.type,
                content: msg.content,
                timestamp: new Date(msg.timestamp).toISOString(),
                metadata: msg.metadata
              }))
            }

            // Update status
            if (progressData.status) {
              taskData.status = progressData.status
            }

            // Extract MCP server information
            const mcpMessages = taskData.messages.filter(msg => 
              msg.metadata?.action?.includes('mcp') || 
              msg.content.includes('MCP')
            )
            
            if (mcpMessages.length > 0) {
              const serverConnectedMessages = mcpMessages.filter(msg => 
                msg.metadata?.action?.includes('ready') || 
                msg.content.includes('connected')
              )
              taskData.mcpServers = serverConnectedMessages.map(msg => 
                msg.content.split(' ')[0] || 'Unknown'
              )
            }

            // Set duration if completed
            if (progressData.status === 'completed' || progressData.status === 'failed') {
              taskData.duration = Date.now() - new Date(taskData.createdAt).getTime()
            }

          } catch (parseError) {
            console.error('Error parsing MCP progress data:', parseError)
          }
        }
      }
    })

    pythonProcess.stderr.on('data', (data: Buffer) => {
      const errorOutput = data.toString()
      console.error('MCP Python script error:', errorOutput)
      
      // Add error message to task
      taskData.messages.push({
        id: uuidv4(),
        type: 'system',
        content: `Error: ${errorOutput}`,
        timestamp: new Date().toISOString(),
        metadata: { action: 'error' }
      })
    })

    pythonProcess.on('close', (code: number) => {
      console.log(`MCP Python script exited with code ${code}`)
      
      if (code !== 0 && taskData.status === 'running') {
        taskData.status = 'failed'
        taskData.messages.push({
          id: uuidv4(),
          type: 'system',
          content: `Task failed with exit code ${code}`,
          timestamp: new Date().toISOString(),
          metadata: { action: 'task_failed', exit_code: code }
        })
      } else if (taskData.status === 'running') {
        taskData.status = 'completed'
      }

      // Set final duration
      taskData.duration = Date.now() - new Date(taskData.createdAt).getTime()
    })

    pythonProcess.on('error', (error: Error) => {
      console.error('Failed to start MCP Python script:', error)
      taskData.status = 'failed'
      taskData.messages.push({
        id: uuidv4(),
        type: 'system',
        content: `Failed to start MCP automation: ${error.message}`,
        timestamp: new Date().toISOString(),
        metadata: { action: 'startup_error' }
      })
    })

  } catch (error) {
    console.error('Error in MCP task execution:', error)
    taskData.status = 'failed'
    taskData.messages.push({
      id: uuidv4(),
      type: 'system',
      content: `Execution error: ${error}`,
      timestamp: new Date().toISOString(),
      metadata: { action: 'execution_error' }
    })
  }
}

// Cleanup old tasks (optional)
setInterval(() => {
  const now = Date.now()
  const maxAge = 24 * 60 * 60 * 1000 // 24 hours

  for (const [taskId, task] of mcpTasks.entries()) {
    const taskAge = now - new Date(task.createdAt).getTime()
    if (taskAge > maxAge) {
      mcpTasks.delete(taskId)
    }
  }
}, 60 * 60 * 1000) // Run cleanup every hour
