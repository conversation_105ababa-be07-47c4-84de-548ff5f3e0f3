import { NextRequest, NextResponse } from "next/server";
import { TaskManager, ProcessHandler, initServices } from "@/lib/browser-use";

// Inicializar serviços quando o módulo for carregado
initServices();

export async function POST(request: NextRequest) {
  try {
    const { task, config } = await request.json();

    if (!task || typeof task !== "string") {
      return NextResponse.json({ error: "Task is required" }, { status: 400 });
    }

    // Validate and create task config
    const taskConfig = TaskManager.validateConfig(config);

    // Create new task
    const newTask = TaskManager.createTask(task, taskConfig);

    // Start the enhanced browser task
    ProcessHandler.startEnhancedBrowserTask(newTask.id, task, taskConfig);

    return NextResponse.json({ task: newTask });
  } catch (error) {
    console.error("Error creating enhanced browser task:", error);
    return NextResponse.json(
      { error: "Failed to create enhanced browser task" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get("id");

  if (taskId) {
    const task = TaskManager.getTask(taskId);
    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 });
    }
    return NextResponse.json({ task });
  }

  // Return all tasks
  const allTasks = TaskManager.getAllTasks();
  return NextResponse.json({ tasks: allTasks });
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get("id");

  if (!taskId) {
    return NextResponse.json({ error: "Task ID is required" }, { status: 400 });
  }

  const task = TaskManager.getTask(taskId);
  if (!task) {
    return NextResponse.json({ error: "Task not found" }, { status: 404 });
  }

  try {
    TaskManager.deleteTask(taskId);
    return NextResponse.json({ message: "Task deleted successfully" });
  } catch (error) {
    console.error("Failed to delete task:", error);
    return NextResponse.json(
      { error: "Failed to delete task" },
      { status: 500 }
    );
  }
}
