'use client'

import { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, Settings, Zap, Database, Github, FileText, Loader2 } from 'lucide-react'

interface MCPTaskMessage {
  id: string
  type: 'system' | 'assistant' | 'user'
  content: string
  timestamp: string
  metadata?: {
    action?: string
    mcp_servers?: number
    servers_connected?: number
    [key: string]: any
  }
}

interface MCPBrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: MCPTaskMessage[]
  createdAt: string
  duration?: number
  mcpServers?: string[]
}

export default function MCPPage() {
  const [task, setTask] = useState('')
  const [geminiApiKey, setGeminiApiKey] = useState('')
  const [currentTask, setCurrentTask] = useState<MCPBrowserTask | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [currentTask?.messages])

  useEffect(() => {
    // Load API key from localStorage
    const savedApiKey = localStorage.getItem('gemini_api_key')
    if (savedApiKey) {
      setGeminiApiKey(savedApiKey)
    }
  }, [])

  const saveApiKey = () => {
    localStorage.setItem('gemini_api_key', geminiApiKey)
    setShowSettings(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!task.trim() || isLoading) return

    if (!geminiApiKey.trim()) {
      alert('Please configure your Gemini API key in settings first.')
      setShowSettings(true)
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/mcp-task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          task: task.trim(),
          geminiApiKey: geminiApiKey.trim()
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create MCP task')
      }

      const data = await response.json()
      setCurrentTask(data.task)
      setTask('')

      // Start polling for updates
      pollTaskUpdates(data.task.id)
    } catch (error) {
      console.error('Error creating MCP task:', error)
      alert('Failed to create MCP task. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const pollTaskUpdates = async (taskId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/mcp-task?id=${taskId}`)
        if (response.ok) {
          const data = await response.json()
          setCurrentTask(data.task)

          // Continue polling if task is still running
          if (data.task.status === 'running' || data.task.status === 'pending') {
            setTimeout(poll, 1000) // Poll every second
          }
        }
      } catch (error) {
        console.error('Error polling task updates:', error)
        // Retry after a longer delay
        setTimeout(poll, 5000)
      }
    }

    poll()
  }

  const getMessageIcon = (type: string, metadata?: any) => {
    if (type === 'system') {
      if (metadata?.action?.includes('mcp')) {
        return <Database className="w-4 h-4 text-purple-500" />
      }
      return <Settings className="w-4 h-4 text-gray-500" />
    }
    if (type === 'assistant') return <Bot className="w-4 h-4 text-blue-500" />
    return <User className="w-4 h-4 text-green-500" />
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'running': return 'text-blue-600 bg-blue-100'
      case 'completed': return 'text-green-600 bg-green-100'
      case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getMCPServerIcon = (serverName: string) => {
    if (serverName.includes('github')) return <Github className="w-4 h-4" />
    if (serverName.includes('filesystem')) return <FileText className="w-4 h-4" />
    if (serverName.includes('sqlite')) return <Database className="w-4 h-4" />
    return <Zap className="w-4 h-4" />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Zap className="w-8 h-8 text-purple-600" />
            <h1 className="text-4xl font-bold text-gray-800">Browser-Use MCP</h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Advanced browser automation powered by Gemini AI and Model Context Protocol (MCP) 
            with filesystem, GitHub, and database integration capabilities.
          </p>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">MCP Configuration</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Gemini API Key
                  </label>
                  <input
                    type="password"
                    value={geminiApiKey}
                    onChange={(e) => setGeminiApiKey(e.target.value)}
                    placeholder="Enter your Gemini API key"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Get your API key from Google AI Studio
                  </p>
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button
                  onClick={saveApiKey}
                  className="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  Save
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="max-w-4xl mx-auto">
          {/* Task Status */}
          {currentTask && (
            <div className="bg-white rounded-lg shadow-sm border mb-6 p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(currentTask.status)}`}>
                    {currentTask.status.toUpperCase()}
                  </span>
                  {currentTask.status === 'running' && (
                    <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  {currentTask.duration ? `${Math.round(currentTask.duration / 1000)}s` : 'Running...'}
                </div>
              </div>
              
              {/* MCP Servers Status */}
              {currentTask.mcpServers && currentTask.mcpServers.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>MCP Servers:</span>
                  {currentTask.mcpServers.map((server, index) => (
                    <div key={index} className="flex items-center gap-1 bg-purple-100 px-2 py-1 rounded">
                      {getMCPServerIcon(server)}
                      <span>{server}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Messages */}
          <div className="bg-white rounded-lg shadow-sm border mb-6">
            <div className="h-96 overflow-y-auto p-4 space-y-4">
              {currentTask?.messages.map((message) => (
                <div key={message.id} className="flex gap-3">
                  <div className="flex-shrink-0 mt-1">
                    {getMessageIcon(message.type, message.metadata)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {message.type}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                      {message.metadata?.action && (
                        <span className="text-xs bg-gray-100 px-2 py-0.5 rounded">
                          {message.metadata.action}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                      {message.content}
                    </div>
                  </div>
                </div>
              ))}
              {!currentTask && (
                <div className="text-center text-gray-500 py-8">
                  <Zap className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Enter a task below to start MCP-enabled browser automation</p>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input Form */}
          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex gap-3">
              <input
                type="text"
                value={task}
                onChange={(e) => setTask(e.target.value)}
                placeholder="Enter your browser automation task (e.g., 'Search for AI news and save results to a file')"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowSettings(true)}
                className="px-4 py-3 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors"
                title="Settings"
              >
                <Settings className="w-5 h-5" />
              </button>
              <button
                type="submit"
                disabled={isLoading || !task.trim()}
                className="px-6 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
                {isLoading ? 'Running...' : 'Run Task'}
              </button>
            </div>
          </form>

          {/* MCP Info */}
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>
              Powered by <strong>Gemini AI</strong> with <strong>MCP integration</strong> for 
              filesystem, GitHub, and database operations
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
