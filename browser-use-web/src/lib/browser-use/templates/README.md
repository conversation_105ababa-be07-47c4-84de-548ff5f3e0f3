# Browser-Use Script Templates

Esta pasta contém os templates modulares para geração de scripts Python do browser-use.

## Estrutura

### Ficheiros Principais
- `base-template.ts` - Template base com placeholders para todas as secções
- `script-assembler.ts` - Classe responsável por montar o script final
- `index.ts` - Exporta todos os templates

### Secções Modulares
- `imports-section.ts` - Importações e setup inicial do browser-use
- `llm-setup-section.ts` - Configuração do modelo LLM
- `browser-setup-section.ts` - Configuração da sessão do browser
- `domain-restrictions.ts` - Lógica de restrições de domínio
- `agent-setup-section.ts` - Configuração do agente
- `execution-section.ts` - Execução da tarefa e captura de screenshots
- `content-extraction-section.ts` - Extração e formatação do conteúdo
- `cleanup-section.ts` - Limpeza e encerramento

## Como Funciona

1. **ScriptAssembler** recebe os parâmetros da tarefa
2. Substitui os tokens `{{TOKEN}}` pelos valores reais
3. Monta as secções na ordem correta
4. Retorna o script Python completo

## Vantagens da Modularização

- **Manutenção** - Cada secção pode ser editada independentemente
- **Reutilização** - Secções podem ser reutilizadas em diferentes contextos
- **Testabilidade** - Cada secção pode ser testada isoladamente
- **Legibilidade** - Código mais organizado e fácil de entender
- **Extensibilidade** - Fácil adicionar novas secções ou modificar existentes

## Tokens Disponíveis

- `{{TASK_ID}}` - ID único da tarefa
- `{{TASK}}` - Descrição da tarefa
- `{{PROVIDER}}` - Provedor LLM (openai, anthropic, google)
- `{{MODEL}}` - Modelo específico
- `{{API_KEY}}` - Chave da API
- `{{TEMPERATURE}}` - Temperatura do modelo
- `{{HEADLESS}}` - Modo headless do browser
- `{{USE_VISION}}` - Usar capacidades de visão
- `{{MAX_STEPS}}` - Número máximo de passos
- `{{ALLOWED_DOMAINS}}` - Lista de domínios permitidos

## Exemplo de Uso

```typescript
import { ScriptAssembler } from './script-assembler';

const script = ScriptAssembler.assembleScript(
  'task-123',
  'Pesquisar sobre IA',
  {
    provider: 'openai',
    model: 'gpt-4',
    // ... outras configurações
  }
);
```