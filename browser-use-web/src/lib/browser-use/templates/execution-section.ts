export const EXECUTION_SECTION = `# Execute task
        log_progress('assistant', f'🎯 Executing task: {{TASK}}')
        
        # Add callback to capture screenshots during execution
        import base64
        
        # Hook into the agent's step method to capture screenshots
        original_step = agent.step if hasattr(agent, 'step') else None
        step_count = 0
        
        if original_step:
            async def screenshot_step(*args, **kwargs):
                nonlocal step_count
                step_count += 1
                log_progress('system', f'📍 Passo {step_count}: Analisando página e planeando ação...')
                
                # Take screenshot before step
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if page:
                        # Get page info and take a small screenshot
                        url = page.url
                        title = await page.title()
                        
                        try:
                            # Take a high-quality full page screenshot
                            screenshot = await page.screenshot(
                                type='png',  # PNG for better quality
                                full_page=True,  # Capture the entire page
                                clip=None  # No clipping for full page
                            )

                            # Convert to base64 directly
                            screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')

                            # If screenshot is too large, try JPEG with high quality
                            if len(screenshot_b64) > 800000:  # 800KB limit for PNG
                                try:
                                    # Try JPEG with high quality for better compression
                                    compressed_screenshot = await page.screenshot(
                                        type='jpeg',
                                        quality=85,  # High quality JPEG
                                        full_page=True
                                    )
                                    screenshot_b64 = base64.b64encode(compressed_screenshot).decode('utf-8')
                                    log_progress('system', f'📸 Screenshot convertido para JPEG de alta qualidade')

                                    # If still too large, reduce quality slightly
                                    if len(screenshot_b64) > 600000:  # 600KB limit for JPEG
                                        final_screenshot = await page.screenshot(
                                            type='jpeg',
                                            quality=70,  # Good quality JPEG
                                            full_page=True
                                        )
                                        screenshot_b64 = base64.b64encode(final_screenshot).decode('utf-8')
                                        log_progress('system', f'📸 Screenshot otimizado para melhor performance')
                                except Exception as compress_error:
                                    log_progress('system', f'⚠️ Erro ao comprimir screenshot: {str(compress_error)}')
                            
                            # Send the screenshot
                            screenshot_data = {
                                'task_id': '{{TASK_ID}}',
                                'screenshot': screenshot_b64,
                                'url': url,
                                'title': title,
                                'timestamp': time.time() * 1000
                            }
                            print(f"BROWSER_USE_SCREENSHOT:{json.dumps(screenshot_data)}")
                            sys.stdout.flush()
                            log_progress('system', f'📸 Página visitada: {title} (screenshot completo capturado)')
                        except Exception as screenshot_error:
                            log_progress('system', f'📸 Página visitada: {title} (erro no screenshot: {str(screenshot_error)})')
                        
                        # Log URL change
                        url_data = {
                            'task_id': '{{TASK_ID}}',
                            'url': url,
                            'title': title,
                            'timestamp': time.time() * 1000
                        }
                        print(f"BROWSER_USE_URL:{json.dumps(url_data)}")
                        sys.stdout.flush()
                        
                except Exception as screenshot_error:
                    log_progress('system', f'⚠️ Não foi possível capturar screenshot: {str(screenshot_error)}')
                
                # Execute original step
                result = await original_step(*args, **kwargs)
                log_progress('system', f'✅ Passo {step_count} concluído')
                return result
            
            # Replace the step method
            agent.step = screenshot_step
        
        result = await agent.run()
        
        log_progress('assistant', '🎉 Task completed successfully!')`;