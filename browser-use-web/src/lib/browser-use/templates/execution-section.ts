export const EXECUTION_SECTION = `# Execute task
        log_progress('assistant', f'🎯 Executing task: {{TASK}}')
        
        # Add callback to capture screenshots during execution
        import base64
        
        # Hook into the agent's step method to capture screenshots
        original_step = agent.step if hasattr(agent, 'step') else None
        step_count = 0
        
        if original_step:
            async def screenshot_step(*args, **kwargs):
                nonlocal step_count
                step_count += 1
                log_progress('system', f'📍 Passo {step_count}: Analisando página e planeando ação...')
                
                # Take screenshot before step
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if page:
                        # Get page info and take a small screenshot
                        url = page.url
                        title = await page.title()
                        
                        try:
                            # Take a full page screenshot
                            screenshot = await page.screenshot(
                                type='jpeg', 
                                quality=60,  # Better quality for full page
                                full_page=True  # Capture the entire page
                            )
                            
                            # Convert to base64 directly (without PIL)
                            screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')
                            
                            # Compress the screenshot if it's too large
                            if len(screenshot_b64) > 500000:  # 500KB limit
                                try:
                                    # Try with lower quality but still full page
                                    compressed_screenshot = await page.screenshot(
                                        type='jpeg', 
                                        quality=40,  # Lower quality but still readable
                                        full_page=True  # Keep full page
                                    )
                                    screenshot_b64 = base64.b64encode(compressed_screenshot).decode('utf-8')
                                    log_progress('system', f'📸 Screenshot comprimido para melhor performance')
                                except Exception as compress_error:
                                    log_progress('system', f'⚠️ Erro ao comprimir screenshot: {str(compress_error)}')
                            
                            # Send the screenshot
                            screenshot_data = {
                                'task_id': '{{TASK_ID}}',
                                'screenshot': screenshot_b64,
                                'url': url,
                                'title': title,
                                'timestamp': time.time() * 1000
                            }
                            print(f"BROWSER_USE_SCREENSHOT:{json.dumps(screenshot_data)}")
                            sys.stdout.flush()
                            log_progress('system', f'📸 Página visitada: {title} (screenshot completo capturado)')
                        except Exception as screenshot_error:
                            log_progress('system', f'📸 Página visitada: {title} (erro no screenshot: {str(screenshot_error)})')
                        
                        # Log URL change
                        url_data = {
                            'task_id': '{{TASK_ID}}',
                            'url': url,
                            'title': title,
                            'timestamp': time.time() * 1000
                        }
                        print(f"BROWSER_USE_URL:{json.dumps(url_data)}")
                        sys.stdout.flush()
                        
                except Exception as screenshot_error:
                    log_progress('system', f'⚠️ Não foi possível capturar screenshot: {str(screenshot_error)}')
                
                # Execute original step
                result = await original_step(*args, **kwargs)
                log_progress('system', f'✅ Passo {step_count} concluído')
                return result
            
            # Replace the step method
            agent.step = screenshot_step
        
        result = await agent.run()
        
        log_progress('assistant', '🎉 Task completed successfully!')`;