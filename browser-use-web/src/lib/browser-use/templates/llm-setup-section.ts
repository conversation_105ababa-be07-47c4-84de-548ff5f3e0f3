export const LLM_SETUP_SECTION = `# Setup LLM
        log_progress('system', f'🤖 Setting up {{PROVIDER}} LLM with model {{MODEL}}')
        
        api_key = '{{API_KEY}}' or os.getenv('{{PROVIDER_ENV_KEY}}')
        if not api_key:
            log_progress('system', f'❌ API key not found for {{PROVIDER}}')
            sys.exit(1)
        
        llm = LLMClass(
            model='{{MODEL}}',
            temperature={{TEMPERATURE}},
            api_key=api_key
        )
        log_progress('system', '✅ LLM configured successfully')`;