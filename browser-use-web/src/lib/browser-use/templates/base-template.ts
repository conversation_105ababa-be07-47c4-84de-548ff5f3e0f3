export const BASE_TEMPLATE = `#!/usr/bin/env python3
"""
Generic Browser-Use Script - Direct integration with browser-use library
Generated for task: {{TASK_ID}}
"""

import asyncio
import json
import sys
import time
import os
import re

# Simple progress logging
def log_progress(message_type: str, content: str, metadata: dict = None):
    progress_data = {
        'task_id': '{{TASK_ID}}',
        'type': message_type,
        'content': content,
        'timestamp': time.time() * 1000,
        'metadata': metadata or {}
    }
    print(f"BROWSER_USE_PROGRESS:{json.dumps(progress_data)}")
    sys.stdout.flush()

async def main():
    try:
        log_progress('system', '🔧 Importing browser-use components...')
        
        {{IMPORTS_SECTION}}
        
        {{LLM_SETUP_SECTION}}
        
        {{BROWSER_SETUP_SECTION}}
        
        {{AGENT_SETUP_SECTION}}
        
        {{EXECUTION_SECTION}}
        
        # Extract content based on task type
        task_type = "{{TASK_TYPE}}"
        log_progress('system', f'🔄 Processando resultado para tipo de tarefa: {task_type}')
        
        if task_type == "research":
            # For research tasks, extract and format content
            {{CONTENT_EXTRACTION_SECTION}}
        elif task_type == "form_filling":
            # For form filling tasks, extract form data
            try:
                # Extract form data if available
                form_data = {}
                if hasattr(result, 'form_data'):
                    form_data = result.form_data
                elif isinstance(result, dict) and 'form_data' in result:
                    form_data = result['form_data']
                
                # Create a simple report
                form_report = {
                    "task_type": "form_filling",
                    "success": True,
                    "message": "Formulário preenchido com sucesso",
                    "form_data": form_data,
                    "fields_count": len(form_data) if form_data else 0,
                    "url": browser_session.page.url if hasattr(browser_session, 'page') else None
                }
                
                # Log the result
                log_progress('result', json.dumps(form_report, ensure_ascii=False))
            except Exception as form_error:
                log_progress('system', f'❌ Erro ao processar dados do formulário: {str(form_error)}')
                log_progress('result', str(result))
        else:
            # For general tasks, just log the result
            log_progress('result', str(result))
        
    except Exception as e:
        log_progress('system', f'❌ Task execution failed: {str(e)}')
        import traceback
        log_progress('system', f'❌ Full error: {traceback.format_exc()}')
        sys.exit(1)
    finally:
        {{CLEANUP_SECTION}}

if __name__ == "__main__":
    asyncio.run(main())`;