export const CONTENT_EXTRACTION_SECTION = `# GENERIC CONTENT EXTRACTION
        # Helper function to safely get step_count
        def get_step_count():
            if 'step_count' in locals() or 'step_count' in globals():
                return step_count
            return 0
            
        try:
            log_progress('system', '🔍 Iniciando extração detalhada do conteúdo...')
            
            # Convert result to string for analysis
            result_str = str(result)
            log_progress('system', f'📊 Tamanho total do resultado: {len(result_str)} chars')
            
            # Extract meaningful content
            extracted_info = {}
            
            # Method 1: Split by extracted_content and look for meaningful content
            if 'extracted_content=' in result_str:
                parts = result_str.split('extracted_content=')
                for i, part in enumerate(parts[1:], 1):
                    lines = part.split('\\\\n')[:10]
                    content_lines = []
                    for line in lines:
                        line = line.strip()
                        if len(line) > 20 and not line.startswith(('is_done', 'success', 'error', 'attachments')):
                            content_lines.append(line)
                    
                    if content_lines:
                        content = ' '.join(content_lines)
                        if len(content) > 100:
                            extracted_info[f'extracted_{i}'] = content
                            log_progress('system', f'✅ Conteúdo extraído {i}: {len(content)} chars')
            
            # Method 2: Split by long_term_memory and look for meaningful content
            if 'long_term_memory=' in result_str:
                parts = result_str.split('long_term_memory=')
                for i, part in enumerate(parts[1:], 1):
                    lines = part.split('\\\\n')[:10]
                    content_lines = []
                    for line in lines:
                        line = line.strip()
                        if len(line) > 20 and not line.startswith(('is_done', 'success', 'error', 'attachments')):
                            content_lines.append(line)
                    
                    if content_lines:
                        content = ' '.join(content_lines)
                        if len(content) > 100:
                            extracted_info[f'memory_{i}'] = content
                            log_progress('system', f'✅ Memória extraída {i}: {len(content)} chars')
            
            # Create final report
            if extracted_info:
                # Find the most comprehensive content
                best_content_key = max(extracted_info.keys(), key=lambda k: len(extracted_info[k]))
                best_content = extracted_info[best_content_key]
                
                log_progress('system', f'📊 Melhor conteúdo selecionado: {best_content_key} ({len(best_content)} chars)')
                
                # Extract and clean the main content from the best source
                main_content = best_content
                
                # Try to extract the actual report content if it's embedded
                if 'report.md:' in main_content:
                    # Extract content after report.md:
                    parts = main_content.split('report.md:')
                    if len(parts) > 1:
                        main_content = parts[1].strip()
                elif 'vibe_coding_summary.md:' in main_content:
                    # Extract content after vibe_coding_summary.md:
                    parts = main_content.split('vibe_coding_summary.md:')
                    if len(parts) > 1:
                        main_content = parts[1].strip()
                elif 'summary.md:' in main_content:
                    # Extract content after summary.md:
                    parts = main_content.split('summary.md:')
                    if len(parts) > 1:
                        main_content = parts[1].strip()
                
                # Clean up the content - remove escape characters and format properly
                main_content = main_content.replace('\\\\\\\\n', '\\\\n').replace('\\\\\\\\', '').replace('\\\\\\'', "'")
                
                # Extract only the relevant content (definition, concept, etc.)
                # Look for markdown headers and extract the content
                content_lines = main_content.split('\\\\n')
                cleaned_lines = []
                in_relevant_section = False
                
                # First pass: try to find markdown content
                for line in content_lines:
                    # Skip JSON or technical details
                    if line.strip().startswith('{') or 'extracted_content=' in line or 'Task completed:' in line:
                        continue
                        
                    # Include markdown headers and their content
                    if line.strip().startswith('#'):
                        in_relevant_section = True
                        cleaned_lines.append(line)
                    elif in_relevant_section and line.strip():
                        cleaned_lines.append(line)
                
                # If we couldn't find structured content, try to extract from summary sections
                if not cleaned_lines:
                    for line in content_lines:
                        if "Relatório Detalhado:" in line or "Definição/Conceito" in line or "Resumo:" in line:
                            in_relevant_section = True
                            cleaned_lines.append(line)
                        elif in_relevant_section and line.strip():
                            cleaned_lines.append(line)
                
                # If still no content, use the original but filter out technical parts
                if not cleaned_lines:
                    for line in content_lines:
                        if not any(tech_term in line for tech_term in ['extracted_content=', 'Task completed:', 'memory_', 'timestamp', 'is_done']):
                            if len(line.strip()) > 10:  # Only include meaningful lines
                                cleaned_lines.append(line)
                
                # Process the content to ensure proper line breaks
                processed_content = '\\\\n'.join(cleaned_lines)
                
                # Create a clean, readable report that focuses only on the relevant content
                # Remove any technical metadata or debug information
                final_content = processed_content
                
                # Remove any timestamps or task IDs that might be in the content
                final_content = final_content.replace('19:09:19', '')
                final_content = final_content.replace('Task completed: True -', '')
                final_content = final_content.replace('extracted_content=', '')
                
                # Clean up any remaining technical artifacts
                final_content = final_content.replace('Melhor conteúdo selecionado:', '')
                final_content = final_content.replace('RELATÓRIO DE PESQUISA DETALHADO', '')
                final_content = final_content.replace('RESUMO EXECUTIVO', '')
                final_content = final_content.replace('ESTATÍSTICAS DA PESQUISA', '')
                final_content = final_content.replace('CONTEÚDO COMPLETO', '')
                final_content = final_content.replace('INFORMAÇÕES TÉCNICAS', '')
                final_content = final_content.replace('Relatório gerado automaticamente pelo sistema Browser-Use', '')
                final_content = final_content.replace('---', '')
                
                # Remove any lines with just timestamps or technical info
                clean_lines = []
                for line in final_content.split('\\\\n'):
                    # Skip empty lines at the beginning
                    if not clean_lines and not line.strip():
                        continue
                    # Skip lines that are just timestamps
                    if line.strip() and not line.strip().replace(':', '').isdigit():
                        clean_lines.append(line)
                
                # Join the clean lines back together
                final_content = '\\\\n'.join(clean_lines)
                
                # Create the final clean report with just the content
                # Remove any remaining technical artifacts
                final_content = final_content.replace('Task completed: True -', '')
                final_content = final_content.replace('AgentHistoryList(all_results=', '')
                final_content = final_content.replace('ActionResult(', '')
                final_content = final_content.replace('task_completed', '')
                final_content = final_content.replace('✅ Task completed successfully!', '')

                # Remove timestamps
                import re
                final_content = re.sub(r'\d{2}:\d{2}:\d{2}', '', final_content)

                clean_report = f\"\"\"{final_content.strip()}\"\"\"
                
                # Extract key findings from content
                key_findings = []
                content_lines = main_content.split('\\\\n')
                for line in content_lines:
                    line = line.strip()
                    if line.startswith(('-', '*', '•')) and len(line) > 20:
                        key_findings.append(line.lstrip('-*• '))
                    elif line.startswith(('##', '###')) and len(line) > 10:
                        key_findings.append(line.lstrip('# '))
                
                # Limit key findings to top 10
                limited_key_findings = []
                if len(key_findings) > 0:
                    limited_key_findings = key_findings[:10]
                else:
                    limited_key_findings = ["Informações extraídas da pesquisa web"]
                
                # Extract sources (mock data for now - in real implementation would extract from browser history)
                sources = [
                    {"url": "https://example1.com", "title": "Fonte 1 da pesquisa"},
                    {"url": "https://example2.com", "title": "Fonte 2 da pesquisa"},
                    {"url": "https://example3.com", "title": "Fonte 3 da pesquisa"}
                ]
                
                # Calculate rating based on content quality and completeness
                rating = 8.5 if len(best_content) > 10000 else 7.0 if len(best_content) > 5000 else 6.0
                confidence_score = 0.9 if len(extracted_info) > 10 else 0.8 if len(extracted_info) > 5 else 0.7
                
                # Create structured JSON report following the schema
                structured_json_report = {
                    "title": f"Pesquisa: {{TASK}}",
                    "summary": main_content[:500] + "..." if len(main_content) > 500 else main_content,
                    "key_findings": limited_key_findings,
                    "sources": sources,
                    "rating": rating,
                    "confidence_score": confidence_score,
                    "metadata": {
                        "pages_analyzed": step_count,
                        "content_sources": len(extracted_info),
                        "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "model_used": "{{MODEL}}"
                    }
                }
                
                # Send the clean report as the main result
                log_progress('result', clean_report)
                
                # Also send structured JSON following the schema
                log_progress('json_data', json.dumps(structured_json_report, ensure_ascii=False, indent=2))
                
            else:
                # Fallback if no content extracted
                log_progress('system', '⚠️ Nenhum conteúdo específico encontrado, criando relatório básico...')
                
                fallback_report = {
                    "metadata": {
                        "title": "Relatório de Pesquisa",
                        "task": "{{TASK}}",
                        "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "model_used": "{{MODEL}}",
                        "pages_analyzed": step_count,
                        "status": "completed_with_basic_extraction"
                    },
                    "summary": f"Pesquisa executada com sucesso. {step_count} páginas foram analisadas.",
                    "research_statistics": {
                        "pages_visited": step_count,
                        "task_completed": True
                    },
                    "note": "Conteúdo detalhado disponível nos logs de execução."
                }
                
                json_report = json.dumps(fallback_report, ensure_ascii=False, indent=2)
                log_progress('result', json_report)
                
        except Exception as format_error:
            log_progress('system', f'❌ Erro na extração: {str(format_error)}')
            
            # Get step count safely
            visit_count = get_step_count()
            
            # Emergency fallback
            emergency_report = {
                "metadata": {
                    "title": "Erro na Extração de Conteúdo",
                    "task": "{{TASK}}",
                    "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "model_used": "{{MODEL}}",
                    "status": "error"
                },
                "error": {
                    "message": "Pesquisa executada mas houve erro na extração do conteúdo",
                    "pages_visited": visit_count,
                    "error_details": str(format_error),
                    "suggestion": "Tente executar a pesquisa novamente ou use termos mais específicos"
                }
            }
            
            json_report = json.dumps(emergency_report, ensure_ascii=False, indent=2)
            log_progress('result', json_report)`;