export const DOMAIN_RESTRICTIONS_SECTION = `# Domain restriction logic
        allowed_domains = {{ALLOWED_DOMAINS}}
        domain_restriction = ""
        
        if allowed_domains and len(allowed_domains) > 0:
            domain_list = ", ".join(allowed_domains)
            domain_restriction = f"""
RESTRIÇÃO DE DOMÍNIOS CRÍTICA:
- Você DEVE visitar APENAS os seguintes domínios: {domain_list}
- NÃO visite nenhum outro site fora desta lista
- Se precisar de pesquisar, use apenas sites da lista permitida
- Se não encontrar informação suficiente nos domínios permitidos, indique isso no relatório
"""
            log_progress('system', f'🔒 Domínios restritos a: {domain_list}')
        else:
            log_progress('system', '🌐 Sem restrições de domínio - pode visitar qualquer site')
        
        # Create a more detailed task prompt to ensure content extraction
        enhanced_task = f"""TAREFA ESPECÍFICA: {{TASK}}

{domain_restriction}

INSTRUÇÕES OBRIGATÓRIAS:
1. Execute a tarefa solicitada visitando sites relevantes{' (respeitando as restrições de domínio acima)' if allowed_domains and len(allowed_domains) > 0 else ''}
2. Leia COMPLETAMENTE o conteúdo de cada página visitada
3. EXTRAIA e DOCUMENTE todas as informações encontradas
4. Crie um RESUMO DETALHADO estruturado com:

## Definição/Conceito Principal
[Definição completa baseada na pesquisa]

## Informações Principais
[Lista detalhada das informações identificadas]

## Características e Detalhes
[Benefícios, funcionalidades, exemplos práticos]

## Pontos de Atenção
[Limitações, riscos ou considerações importantes]

## Fontes Consultadas
[URLs e títulos das páginas visitadas]

CRÍTICO: Você DEVE apresentar um relatório completo e detalhado. NÃO termine com apenas "pesquisa concluída". 
O seu objetivo é criar um documento informativo completo sobre o tema solicitado."""
        
        # Apply domain restrictions if specified
        if allowed_domains and len(allowed_domains) > 0:
            log_progress('system', f'🔒 Aplicando restrições de domínio: {", ".join(allowed_domains)}')
            
            # Create a domain validation function
            def is_domain_allowed(url):
                from urllib.parse import urlparse
                try:
                    parsed_url = urlparse(url)
                    domain = parsed_url.netloc.lower()
                    
                    # Remove www. prefix for comparison
                    if domain.startswith('www.'):
                        domain = domain[4:]
                    
                    # Check if domain is in allowed list
                    for allowed_domain in allowed_domains:
                        allowed_domain_clean = allowed_domain.lower()
                        if allowed_domain_clean.startswith('www.'):
                            allowed_domain_clean = allowed_domain_clean[4:]
                        
                        if domain == allowed_domain_clean or domain.endswith('.' + allowed_domain_clean):
                            return True
                    return False
                except:
                    return False
            
            # Override browser session methods to enforce domain restrictions
            original_browser_session = browser_session
            
            # Create a completely new class that doesn't try to copy attributes
            class RestrictedBrowserSession:
                def __init__(self, original_session):
                    self._original = original_session
                    # Don't try to copy attributes, just store the original session
                
                async def start(self):
                    result = await self._original.start()
                    # Override page navigation after session starts
                    if hasattr(self._original, 'page') and self._original.page:
                        original_goto = self._original.page.goto
                        
                        async def restricted_goto(url, **kwargs):
                            if not is_domain_allowed(url):
                                log_progress('system', f'🚫 Domínio bloqueado: {url}')
                                raise Exception(f"Acesso negado: {url} não está na lista de domínios permitidos")
                            
                            log_progress('system', f'✅ Domínio permitido: {url}')
                            return await original_goto(url, **kwargs)
                        
                        self._original.page.goto = restricted_goto
                    return result
                
                async def close(self):
                    return await self._original.close()
                
                # Forward all attribute access to the original session
                def __getattr__(self, name):
                    return getattr(self._original, name)
                
                # Forward all attribute setting to the original session
                def __setattr__(self, name, value):
                    if name == '_original':
                        # Only set _original on self
                        super().__setattr__(name, value)
                    else:
                        # Forward all other attribute setting to original
                        setattr(self._original, name, value)
            
            # Replace browser session with restricted version
            browser_session = RestrictedBrowserSession(browser_session)`;