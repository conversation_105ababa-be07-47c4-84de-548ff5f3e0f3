export const AGENT_SETUP_SECTION = `# Setup agent with enhanced task prompt and domain restrictions
        log_progress('system', '🤖 Setting up agent...')
        
        # Determine task type
        task_type = "{{TASK_TYPE}}"
        log_progress('system', f'📋 Tipo de tarefa: {task_type}')
        
        {{DOMAIN_RESTRICTIONS_SECTION}}
        
        agent = Agent(
            task=enhanced_task,
            llm=llm,
            browser_session=browser_session,
            use_vision={{USE_VISION}},
            max_steps={{MAX_STEPS}}
        )
        
        {{FORM_FILLING_SECTION}}
        
        log_progress('system', '✅ Agent configured successfully')`;