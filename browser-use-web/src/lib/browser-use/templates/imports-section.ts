export const IMPORTS_SECTION = `# Import browser-use components with error handling
        try:
            from browser_use import Agent
            from browser_use.browser.session import BrowserSession
            import re  # Import regex module for content cleaning
            log_progress('system', '✅ Successfully imported browser-use core components')
        except ImportError:
            try:
                from browser_use import Agent
                from browser_use.browser import BrowserSession
                import re  # Import regex module for content cleaning
                log_progress('system', '✅ Successfully imported browser-use components (fallback)')
            except ImportError as e:
                log_progress('system', f'❌ Failed to import browser-use: {str(e)}')
                sys.exit(1)
        
        # Import LLM components
        try:
            provider = '{{PROVIDER}}'
            if provider == 'openai':
                try:
                    from browser_use.llm.openai.chat import Chat<PERSON>pen<PERSON>I as LLMClass
                except ImportError:
                    from browser_use.llm.openai import Chat<PERSON><PERSON><PERSON>I as LLMClass
            elif provider == 'anthropic':
                try:
                    from browser_use.llm.anthropic.chat import ChatAnthropic as LLMClass
                except ImportError:
                    from browser_use.llm.anthropic import ChatAnthropic as LLMClass
            elif provider == 'google':
                try:
                    from browser_use.llm.google.chat import Chat<PERSON>oogle as LLMClass
                except ImportError:
                    from browser_use.llm.google import ChatGoogle as LLMClass
            else:
                raise ValueError(f"Unsupported provider: {provider}")
            
            log_progress('system', f'✅ Successfully imported {provider} LLM')
        except ImportError as e:
            log_progress('system', f'❌ Failed to import {provider} LLM: {str(e)}')
            sys.exit(1)`;