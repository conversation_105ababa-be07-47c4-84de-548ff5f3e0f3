export const FORM_FILLING_SECTION = `# FORM FILLING AGENT SETUP
        if task_type == "form_filling":
            log_progress('system', '🧩 Configurando agente de preenchimento de formulários...')
            
            # Define form filling specific functions
            async def fill_form(form_data, selector_strategy="css"):
                """
                Fill a form with the provided data
                
                Args:
                    form_data: Dictionary with field selectors as keys and values to fill
                    selector_strategy: Strategy to use for selectors (css, xpath, id, name)
                
                Returns:
                    Success status and message
                """
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if not page:
                        return {"success": False, "message": "Não foi possível obter a página atual"}
                    
                    # Take a screenshot before filling the form
                    await take_full_screenshot("before_form_filling")
                    
                    # Fill each field
                    for selector, value in form_data.items():
                        try:
                            # Wait for the element to be visible
                            if selector_strategy == "css":
                                await page.wait_for_selector(selector, state="visible", timeout=5000)
                            elif selector_strategy == "xpath":
                                await page.wait_for_selector(f"xpath={selector}", state="visible", timeout=5000)
                            elif selector_strategy == "id":
                                await page.wait_for_selector(f"#{selector}", state="visible", timeout=5000)
                            elif selector_strategy == "name":
                                await page.wait_for_selector(f"[name='{selector}']", state="visible", timeout=5000)
                            
                            # Clear the field first (if it's an input or textarea)
                            if selector_strategy == "css":
                                element_type = await page.evaluate(f"document.querySelector('{selector}')?.tagName.toLowerCase()")
                            elif selector_strategy == "xpath":
                                element_type = await page.evaluate(f"document.evaluate('{selector}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue?.tagName.toLowerCase()")
                            elif selector_strategy == "id":
                                element_type = await page.evaluate(f"document.getElementById('{selector}')?.tagName.toLowerCase()")
                            elif selector_strategy == "name":
                                element_type = await page.evaluate(f"document.getElementsByName('{selector}')[0]?.tagName.toLowerCase()")
                            
                            # Clear field if it's an input or textarea
                            if element_type in ["input", "textarea"]:
                                if selector_strategy == "css":
                                    await page.evaluate(f"document.querySelector('{selector}').value = ''")
                                elif selector_strategy == "xpath":
                                    await page.evaluate(f"document.evaluate('{selector}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.value = ''")
                                elif selector_strategy == "id":
                                    await page.evaluate(f"document.getElementById('{selector}').value = ''")
                                elif selector_strategy == "name":
                                    await page.evaluate(f"document.getElementsByName('{selector}')[0].value = ''")
                            
                            # Fill the field
                            if selector_strategy == "css":
                                await page.fill(selector, value)
                            elif selector_strategy == "xpath":
                                await page.fill(f"xpath={selector}", value)
                            elif selector_strategy == "id":
                                await page.fill(f"#{selector}", value)
                            elif selector_strategy == "name":
                                await page.fill(f"[name='{selector}']", value)
                                
                            log_progress('system', f'✅ Campo preenchido: {selector} = {value}')
                        except Exception as field_error:
                            log_progress('system', f'⚠️ Erro ao preencher campo {selector}: {str(field_error)}')
                    
                    # Take a screenshot after filling the form
                    await take_full_screenshot("after_form_filling")
                    
                    return {"success": True, "message": f"Formulário preenchido com {len(form_data)} campos"}
                except Exception as form_error:
                    return {"success": False, "message": f"Erro ao preencher formulário: {str(form_error)}"}
            
            async def submit_form(submit_selector, selector_strategy="css"):
                """
                Submit a form by clicking on the submit button
                
                Args:
                    submit_selector: Selector for the submit button
                    selector_strategy: Strategy to use for selectors (css, xpath, id, name)
                
                Returns:
                    Success status and message
                """
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if not page:
                        return {"success": False, "message": "Não foi possível obter a página atual"}
                    
                    # Wait for the submit button to be visible
                    if selector_strategy == "css":
                        await page.wait_for_selector(submit_selector, state="visible", timeout=5000)
                    elif selector_strategy == "xpath":
                        await page.wait_for_selector(f"xpath={submit_selector}", state="visible", timeout=5000)
                    elif selector_strategy == "id":
                        await page.wait_for_selector(f"#{submit_selector}", state="visible", timeout=5000)
                    elif selector_strategy == "name":
                        await page.wait_for_selector(f"[name='{submit_selector}']", state="visible", timeout=5000)
                    
                    # Click the submit button
                    if selector_strategy == "css":
                        await page.click(submit_selector)
                    elif selector_strategy == "xpath":
                        await page.click(f"xpath={submit_selector}")
                    elif selector_strategy == "id":
                        await page.click(f"#{submit_selector}")
                    elif selector_strategy == "name":
                        await page.click(f"[name='{submit_selector}']")
                    
                    # Wait for navigation to complete
                    await page.wait_for_load_state("networkidle")
                    
                    # Take a screenshot after submission
                    await take_full_screenshot("after_form_submission")
                    
                    return {"success": True, "message": "Formulário enviado com sucesso"}
                except Exception as submit_error:
                    return {"success": False, "message": f"Erro ao enviar formulário: {str(submit_error)}"}
            
            async def take_full_screenshot(action_name):
                """Take a full page screenshot and log it"""
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if page:
                        # Take a full page screenshot
                        screenshot = await page.screenshot(
                            type='jpeg', 
                            quality=70,
                            full_page=True
                        )
                        
                        # Convert to base64
                        screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')
                        
                        # Send screenshot data
                        url = page.url
                        title = await page.title()
                        
                        screenshot_data = {
                            'task_id': '{{TASK_ID}}',
                            'screenshot': screenshot_b64,
                            'url': url,
                            'title': title,
                            'action': action_name,
                            'timestamp': time.time() * 1000
                        }
                        print(f"BROWSER_USE_SCREENSHOT:{json.dumps(screenshot_data)}")
                        sys.stdout.flush()
                        log_progress('system', f'📸 Screenshot capturado: {action_name}')
                except Exception as screenshot_error:
                    log_progress('system', f'⚠️ Erro ao capturar screenshot: {str(screenshot_error)}')
            
            # Add form filling functions to the agent's tools
            agent.tools.extend([
                {
                    "name": "fill_form",
                    "description": "Fill a form with the provided data",
                    "function": fill_form
                },
                {
                    "name": "submit_form",
                    "description": "Submit a form by clicking on the submit button",
                    "function": submit_form
                }
            ])
            
            log_progress('system', '✅ Agente de preenchimento de formulários configurado com sucesso')
        `;