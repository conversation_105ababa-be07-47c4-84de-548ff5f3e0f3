export interface EnhancedBrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: TaskMessage[]
  createdAt: string
  duration?: number
  config: BrowserTaskConfig
  screenshots: string[]
  currentUrl?: string
  pageTitle?: string
}

export interface TaskMessage {
  id: string
  type: 'user' | 'assistant' | 'system' | 'result'
  content: string
  timestamp: string
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
    step?: number
    element_count?: number
    page_analysis?: any
    [key: string]: any
  }
}

export interface BrowserTaskConfig {
  model: string
  provider: 'openai' | 'anthropic' | 'google'
  temperature: number
  headless: boolean
  use_vision: boolean
  max_steps: number
  api_key?: string
  allowed_domains?: string[]
  system_prompt?: string
  user_data_dir?: string
  disable_security?: boolean
  window_width?: number
  window_height?: number
  task_type?: 'research' | 'form_filling' | 'general'
}

export interface ProgressData {
  task_id: string
  type: 'user' | 'assistant' | 'system' | 'result' | string
  content: string
  timestamp: number
  metadata: Record<string, any>
}

export interface ScreenshotData {
  task_id: string
  screenshot: string
  url: string
  title: string
  timestamp: number
}

export interface UrlData {
  task_id: string
  url: string
  title: string
  timestamp: number
}