/**
 * Inicialização de serviços do Browser-Use
 */
import { CleanupService } from './cleanup-service';

// Variável para controlar se os serviços já foram inicializados
let servicesInitialized = false;

/**
 * Inicializa todos os serviços necessários para o Browser-Use
 */
export function initServices(): void {
  // Evitar inicialização duplicada
  if (servicesInitialized) {
    return;
  }
  
  // Iniciar serviço de limpeza automática
  const cleanupService = CleanupService.getInstance();
  cleanupService.start();
  
  // Marcar serviços como inicializados
  servicesInitialized = true;
  
  console.log('🚀 Serviços do Browser-Use inicializados com sucesso');
}

/**
 * Limpa todos os arquivos temporários imediatamente
 */
export function cleanupAllTempFiles(): void {
  const cleanupService = CleanupService.getInstance();
  cleanupService.cleanupAllTempFiles();
}

// Exportar singleton do serviço de limpeza
export const cleanupService = CleanupService.getInstance();