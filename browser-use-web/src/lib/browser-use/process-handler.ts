/**
 * Manipulação de processos Python para o Browser-Use
 */
import { spawn, ChildProcess } from "child_process";
import { BrowserTaskConfig } from "./types";
import { TaskManager } from "./task-manager";
import { ScriptTemplates } from "./script-templates";
import { ScreenshotHandler } from "./screenshot-handler";
import { PageCache } from "./page-cache";

export class ProcessHandler {
  /**
   * Inicia uma tarefa de navegação aprimorada
   * @param taskId ID da tarefa
   * @param task Descrição da tarefa
   * @param config Configuração da tarefa
   */
  static async startEnhancedBrowserTask(
    taskId: string,
    task: string,
    config: BrowserTaskConfig
  ): Promise<void> {
    const taskData = TaskManager.getTask(taskId);
    if (!taskData) return;

    // Update status to running
    TaskManager.updateTaskStatus(taskId, "running");
    TaskManager.addMessage(taskId, {
      type: "system",
      content: `🔧 Initializing browser automation with ${config.model}...`,
      timestamp: new Date().toISOString(),
      metadata: { action: "browser_init" },
    });

    try {
      // Inicializar o cache de páginas
      PageCache.getInstance();

      // Gerar script Python simplificado
      const simplifiedScript = ScriptTemplates.generateSimplifiedScript(
        taskId,
        task,
        config
      );

      // Configurar variáveis de ambiente
      const env = ProcessHandler.setupEnvironment(config);

      // Iniciar processo Python com o script
      const pythonProcess = spawn("/Volumes/Samsung990Pro/miniconda3/envs/BrowserUse/bin/python", ["-c", simplifiedScript], {
        env,
        stdio: ["pipe", "pipe", "pipe"],
      });

      // Configurar handlers para o processo
      ProcessHandler.setupProcessHandlers(pythonProcess, taskId);
    } catch (error) {
      console.error("Error in enhanced task execution:", error);
      TaskManager.updateTaskStatus(taskId, "failed");
      TaskManager.addMessage(taskId, {
        type: "system",
        content: `❌ Execution error: ${error}`,
        timestamp: new Date().toISOString(),
        metadata: { action: "execution_error", error: String(error) },
      });
    }
  }

  /**
   * Configura variáveis de ambiente para o processo Python
   * @param config Configuração da tarefa
   * @returns Variáveis de ambiente
   */
  private static setupEnvironment(
    config: BrowserTaskConfig
  ): NodeJS.ProcessEnv {
    const env = { ...process.env };

    if (config.api_key) {
      if (config.provider === "openai") {
        env.OPENAI_API_KEY = config.api_key;
      } else if (config.provider === "anthropic") {
        env.ANTHROPIC_API_KEY = config.api_key;
      } else if (config.provider === "google") {
        env.GOOGLE_API_KEY = config.api_key;
      }
    }

    return env;
  }

  /**
   * Configura handlers para o processo Python
   * @param pythonProcess Processo Python
   * @param taskId ID da tarefa
   */
  private static setupProcessHandlers(
    pythonProcess: ChildProcess,
    taskId: string
  ): void {
    const taskData = TaskManager.getTask(taskId);
    if (!taskData) return;

    // Handle stdout
    pythonProcess.stdout?.on("data", (data: Buffer) => {
      const output = data.toString();
      const lines = output.split("\n");

      for (const line of lines) {
        ProcessHandler.handleOutputLine(line, taskId);
      }
    });

    // Handle stderr
    pythonProcess.stderr?.on("data", (data: Buffer) => {
      const errorOutput = data.toString();
      console.error("Browser-use script error:", errorOutput);

      TaskManager.addMessage(taskId, {
        type: "system",
        content: `⚠️ Error: ${errorOutput}`,
        timestamp: new Date().toISOString(),
        metadata: { action: "error", error: errorOutput },
      });
    });

    // Handle process close
    pythonProcess.on("close", (code: number) => {
      console.log(`Browser-use script exited with code ${code}`);
      ProcessHandler.handleProcessClose(taskId, code);
    });

    // Handle process error
    pythonProcess.on("error", (error: Error) => {
      console.error("Failed to start browser-use script:", error);
      TaskManager.updateTaskStatus(taskId, "failed");
      TaskManager.addMessage(taskId, {
        type: "system",
        content: `❌ Failed to start browser automation: ${error.message}`,
        timestamp: new Date().toISOString(),
        metadata: { action: "startup_error", error: error.message },
      });
    });
  }

  /**
   * Processa uma linha de saída do processo Python
   * @param line Linha de saída
   * @param taskId ID da tarefa
   */
  private static handleOutputLine(line: string, taskId: string): void {
    if (line.startsWith("BROWSER_USE_PROGRESS:")) {
      ScreenshotHandler.handleProgressData(line, taskId);
    } else if (line.startsWith("BROWSER_USE_SCREENSHOT:")) {
      ScreenshotHandler.handleScreenshotData(line, taskId);
    } else if (line.startsWith("BROWSER_USE_URL:")) {
      ScreenshotHandler.handleUrlData(line, taskId);
    }
  }

  /**
   * Processa o fechamento do processo Python
   * @param taskId ID da tarefa
   * @param code Código de saída
   */
  private static handleProcessClose(taskId: string, code: number): void {
    const taskData = TaskManager.getTask(taskId);
    if (!taskData) return;

    if (code !== 0 && taskData.status === "running") {
      TaskManager.updateTaskStatus(taskId, "failed");
      TaskManager.addMessage(taskId, {
        type: "system",
        content: `❌ Task failed with exit code ${code}`,
        timestamp: new Date().toISOString(),
        metadata: { action: "task_failed", exit_code: code },
      });
    } else if (taskData.status === "running") {
      TaskManager.updateTaskStatus(taskId, "completed");
      TaskManager.addMessage(taskId, {
        type: "assistant",
        content: `✅ Task completed successfully!`,
        timestamp: new Date().toISOString(),
        metadata: { action: "task_completed" },
      });
    }

    // Set final duration
    TaskManager.setTaskDuration(taskId);
  }
}
