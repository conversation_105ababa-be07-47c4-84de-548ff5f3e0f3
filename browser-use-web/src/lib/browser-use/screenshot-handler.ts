/**
 * Manipulação de screenshots para o Browser-Use
 */
import { TaskManager } from './task-manager';

export class ScreenshotHandler {
  /**
   * Processa uma linha de saída do script Python que contém dados de screenshot
   * @param line Linha de saída do script Python
   * @param taskId ID da tarefa
   */
  static handleScreenshotData(line: string, taskId: string): void {
    try {
      // Handle potentially large screenshot data
      const jsonStr = line.substring(23);
      
      // Skip extremely large screenshots to avoid JSON parsing issues
      if (jsonStr.length > 500000) {
        console.log('Skipping extremely large screenshot data to avoid parsing issues');
        TaskManager.addMessage(taskId, {
          type: 'system',
          content: '📸 Screenshot capturado mas muito grande para exibir',
          timestamp: new Date().toISOString(),
          metadata: { action: 'screenshot_too_large', size: jsonStr.length }
        });
        return;
      }
      
      // Try to safely parse the JSON with error handling for malformed JSON
      let screenshotData;
      try {
        screenshotData = JSON.parse(jsonStr);
        
        // Add information about thumbnail status to the message
        const isThumbnail = screenshotData.is_thumbnail;
        const fromCache = screenshotData.from_cache;
        
        let messageContent = `📸 Screenshot capturado: ${screenshotData.title}`;
        if (isThumbnail) {
          messageContent = `📸 Screenshot (thumbnail) capturado: ${screenshotData.title}`;
        }
        if (fromCache) {
          messageContent = `📸 Screenshot do cache: ${screenshotData.title}`;
        }
        
        TaskManager.addMessage(taskId, {
          type: 'system',
          content: messageContent,
          timestamp: new Date().toISOString(),
          metadata: { 
            action: 'screenshot_captured',
            url: screenshotData.url,
            title: screenshotData.title,
            is_thumbnail: isThumbnail,
            from_cache: fromCache
          }
        });
        
        // Add the screenshot to the task
        TaskManager.addScreenshot(taskId, screenshotData);
      } catch (jsonError) {
        // If JSON parsing fails, try to extract just the essential data
        console.log('Attempting to extract screenshot data from malformed JSON');
        
        // Extract task_id
        const taskIdMatch = jsonStr.match(/"task_id"\s*:\s*"([^"]+)"/);
        const extractedTaskId = taskIdMatch ? taskIdMatch[1] : taskId;
        
        // Extract URL
        const urlMatch = jsonStr.match(/"url"\s*:\s*"([^"]+)"/);
        const url = urlMatch ? urlMatch[1] : 'unknown';
        
        // Extract title
        const titleMatch = jsonStr.match(/"title"\s*:\s*"([^"]+)"/);
        const title = titleMatch ? titleMatch[1] : 'Screenshot';
        
        // Create a minimal screenshot data object without the actual screenshot
        const minimalData = {
          task_id: extractedTaskId,
          url: url,
          title: title,
          timestamp: Date.now(),
          screenshot: '' // Empty screenshot data
        };
        
        TaskManager.addMessage(taskId, {
          type: 'system',
          content: `📸 Screenshot capturado de ${title} (sem imagem devido a erro de parsing)`,
          timestamp: new Date().toISOString(),
          metadata: { 
            action: 'screenshot_parse_partial',
            url: url,
            title: title
          }
        });
      }
    } catch (parseError) {
      console.error('Error processing screenshot data:', parseError);
      // Add a message indicating screenshot capture attempt
      TaskManager.addMessage(taskId, {
        type: 'system',
        content: '📸 Screenshot capturado mas houve erro no processamento',
        timestamp: new Date().toISOString(),
        metadata: { 
          action: 'screenshot_parse_error', 
          error: parseError instanceof Error ? parseError.message : String(parseError) 
        }
      });
    }
  }

  /**
   * Processa uma linha de saída do script Python que contém dados de URL
   * @param line Linha de saída do script Python
   * @param taskId ID da tarefa
   */
  static handleUrlData(line: string, taskId: string): void {
    try {
      const urlData = JSON.parse(line.substring(16));
      TaskManager.updateCurrentUrl(taskId, urlData);
    } catch (parseError) {
      console.error('Error parsing URL data:', parseError);
    }
  }

  /**
   * Processa uma linha de saída do script Python que contém dados de progresso
   * @param line Linha de saída do script Python
   * @param taskId ID da tarefa
   */
  static handleProgressData(line: string, taskId: string): void {
    try {
      const progressData = JSON.parse(line.substring(21));
      TaskManager.updateTaskProgress(taskId, progressData);
    } catch (parseError) {
      console.error('Error parsing progress data:', parseError);
    }
  }
}