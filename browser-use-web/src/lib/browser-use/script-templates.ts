/**
 * Templates para geração de scripts Python
 */
import { BrowserTaskConfig } from './types';

export class ScriptTemplates {
  /**
   * Gera um script Python simplificado para execução de tarefas no navegador
   * @param taskId ID da tarefa
   * @param task Descrição da tarefa
   * @param config Configuração da tarefa
   * @returns Script Python
   */
  static generateSimplifiedScript(taskId: string, task: string, config: BrowserTaskConfig): string {
    return `
#!/usr/bin/env python3
import asyncio
import json
import sys
import time
import os

# Simple progress logging
def log_progress(message_type, content, metadata=None):
    progress_data = {
        'task_id': '${taskId}',
        'type': message_type,
        'content': content,
        'timestamp': time.time() * 1000,
        'metadata': metadata or {}
    }
    print(f"BROWSER_USE_PROGRESS:{json.dumps(progress_data)}")
    sys.stdout.flush()

async def main():
    try:
        log_progress('system', '🔧 Importing browser-use components...')
        
        # Import browser-use components
        try:
            from browser_use import Agent
            from browser_use.browser.session import BrowserSession
            log_progress('system', '✅ Successfully imported browser-use components')
        except ImportError:
            try:
                from browser_use import Agent
                from browser_use.browser import BrowserSession
                log_progress('system', '✅ Successfully imported browser-use components (fallback)')
            except ImportError as e:
                log_progress('system', f'❌ Failed to import browser-use: {str(e)}')
                sys.exit(1)
        
        # Import LLM components
        try:
            provider = '${config.provider}'
            if provider == 'openai':
                from browser_use.llm.openai import ChatOpenAI as LLMClass
            elif provider == 'anthropic':
                from browser_use.llm.anthropic import ChatAnthropic as LLMClass
            elif provider == 'google':
                from browser_use.llm.google import ChatGoogle as LLMClass
            else:
                raise ValueError(f"Unsupported provider: {provider}")
            
            log_progress('system', f'✅ Successfully imported {provider} LLM')
        except ImportError as e:
            log_progress('system', f'❌ Failed to import {provider} LLM: {str(e)}')
            sys.exit(1)
        
        # Setup LLM
        log_progress('system', f'🤖 Setting up {provider} LLM with model ${config.model}')
        
        api_key = '${config.api_key || ""}' or os.getenv('${config.provider.toUpperCase()}_API_KEY')
        if not api_key:
            log_progress('system', f'❌ API key not found for {provider}')
            sys.exit(1)
        
        llm = LLMClass(
            model='${config.model}',
            temperature=${config.temperature},
            api_key=api_key
        )
        log_progress('system', '✅ LLM configured successfully')
        
        # Setup browser session
        log_progress('system', '🌐 Setting up browser session...')
        browser_session = BrowserSession(headless=${config.headless ? "True" : "False"})
        await browser_session.start()
        log_progress('system', '✅ Browser session started')
        
        # Setup agent
        log_progress('system', '🤖 Setting up agent...')
        
        # Create task prompt
        task = """${task.replace(/"/g, '\\"')}"""
        
        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
            use_vision=${config.use_vision ? "True" : "False"},
            max_steps=${config.max_steps}
        )
        log_progress('system', '✅ Agent configured successfully')
        
        ${this.generateScreenshotSection(taskId)}
        
        result = await agent.run()
        
        log_progress('assistant', '🎉 Task completed successfully!')
        
        ${this.generateResultProcessingSection(taskId, config)}
        
    except Exception as e:
        log_progress('system', f'❌ Task execution failed: {str(e)}')
        import traceback
        log_progress('system', f'❌ Full error: {traceback.format_exc()}')
        sys.exit(1)
    finally:
        # Cleanup
        try:
            if 'browser_session' in locals():
                await browser_session.close()
                log_progress('system', '🔒 Browser session closed')
        except Exception as cleanup_error:
            log_progress('system', f'⚠️ Error during cleanup: {str(cleanup_error)}')

if __name__ == "__main__":
    asyncio.run(main())
    `;
  }

  /**
   * Gera a seção de captura de screenshots para o script Python
   * @param taskId ID da tarefa
   * @returns Seção de captura de screenshots
   */
  private static generateScreenshotSection(taskId: string): string {
    return `
        # Execute task with screenshot capture
        log_progress('assistant', f'🎯 Executing task: {task}')
        
        # Add screenshot capture functionality
        import base64
        
        # Hook into the agent's step method to capture screenshots
        original_step = agent.step if hasattr(agent, 'step') else None
        step_count = 0
        
        if original_step:
            async def screenshot_step(*args, **kwargs):
                nonlocal step_count
                step_count += 1
                log_progress('system', f'📍 Passo {step_count}: Analisando página e planejando ação...')
                
                # Execute original step first
                result = await original_step(*args, **kwargs)
                
                # Check if page is in cache and use it if available
                try:
                    page = None
                    if hasattr(browser_session, 'get_current_page'):
                        page = await browser_session.get_current_page()
                    elif hasattr(browser_session, 'current_page'):
                        page = browser_session.current_page
                    elif hasattr(browser_session, 'page'):
                        page = browser_session.page
                        
                    if page:
                        # Get page info
                        url = page.url
                        title = await page.title()
                        
                        # Check if page is in cache
                        try:
                            # Import cache module
                            import sys
                            import os
                            import json
                            
                            # Create cache directory if it doesn't exist
                            cache_dir = os.path.join(os.getcwd(), '.cache')
                            if not os.path.exists(cache_dir):
                                os.makedirs(cache_dir)
                            
                            # Generate cache key (MD5 hash of URL)
                            import hashlib
                            cache_key = hashlib.md5(url.encode()).hexdigest()
                            cache_file = os.path.join(cache_dir, f"{cache_key}.json")
                            
                            # Check if cache file exists and is recent (less than 24 hours old)
                            use_cache = False
                            if os.path.exists(cache_file):
                                file_age = time.time() - os.path.getmtime(cache_file)
                                if file_age < 24 * 60 * 60:  # 24 hours in seconds
                                    use_cache = True
                                    log_progress('system', f'🔄 Usando versão em cache da página: {title}')
                            
                            if not use_cache:
                                # Scroll down the page to see more content
                                try:
                                    # Get page height
                                    page_height = await page.evaluate("document.body.scrollHeight")
                                    viewport_height = await page.evaluate("window.innerHeight")
                                    
                                    # Scroll in increments
                                    current_position = 0
                                    max_position = page_height - viewport_height
                                    
                                    # Only scroll if the page is taller than the viewport
                                    if max_position > 0:
                                        # Scroll in 3 steps maximum
                                        scroll_step = max(100, max_position / 3)
                                        
                                        while current_position < max_position:
                                            next_position = min(current_position + scroll_step, max_position)
                                            await page.evaluate(f"window.scrollTo(0, {next_position})")
                                            current_position = next_position
                                            # Small delay to let content load
                                            await asyncio.sleep(0.3)
                                        
                                        # Scroll back to top
                                        await page.evaluate("window.scrollTo(0, 0)")
                                        await asyncio.sleep(0.2)
                                    
                                    log_progress('system', f'📜 Página analisada com scroll completo')
                                except Exception as scroll_error:
                                    log_progress('system', f'⚠️ Erro ao fazer scroll: {str(scroll_error)}')
                                
                                # Take a full page screenshot with optimized settings
                                try:
                                    # Take a high-quality full page screenshot
                                    screenshot = await page.screenshot(
                                        type='png',  # PNG for better quality
                                        full_page=True  # Capture the entire page
                                    )
                                    
                                    # Convert to base64
                                    screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')
                                    
                                    # Get page content for caching
                                    page_content = await page.content()
                                    
                                    # Save to cache
                                    cache_data = {
                                        'url': url,
                                        'title': title,
                                        'content': page_content,
                                        'screenshot': screenshot_b64,
                                        'timestamp': time.time() * 1000
                                    }
                                    
                                    # Write cache to file
                                    with open(cache_file, 'w') as f:
                                        json.dump(cache_data, f)
                                    
                                    log_progress('system', f'💾 Página salva em cache: {title}')
                                    
                                    # Send optimized screenshot
                                    try:
                                        # Create a thumbnail version of the screenshot for sending
                                        # We'll use PIL to resize the image
                                        from PIL import Image
                                        import io
                                        
                                        # Convert bytes to image
                                        img = Image.open(io.BytesIO(screenshot))
                                        
                                        # Calculate new dimensions (50% of original)
                                        width, height = img.size
                                        new_width = width // 2
                                        new_height = height // 2
                                        
                                        # Resize image
                                        img = img.resize((new_width, new_height), Image.LANCZOS)
                                        
                                        # Save to bytes
                                        buffer = io.BytesIO()
                                        img.save(buffer, format='JPEG', quality=30)
                                        buffer.seek(0)
                                        
                                        # Convert to base64
                                        thumbnail_b64 = base64.b64encode(buffer.read()).decode('utf-8')
                                        
                                        # Send the thumbnail if it's not too large
                                        if len(thumbnail_b64) < 200000:  # 200KB limit for better quality
                                            screenshot_data = {
                                                'task_id': '${taskId}',
                                                'screenshot': thumbnail_b64,
                                                'url': url,
                                                'title': title,
                                                'timestamp': time.time() * 1000,
                                                'is_thumbnail': True
                                            }

                                            # Use ensure_ascii for safer JSON
                                            json_data = json.dumps(screenshot_data, ensure_ascii=True)
                                            print(f"BROWSER_USE_SCREENSHOT:{json_data}")
                                            sys.stdout.flush()

                                            log_progress('system', f'📸 Screenshot de alta qualidade enviado: {title}')
                                        else:
                                            # Try with JPEG compression if PNG is too large
                                            try:
                                                jpeg_screenshot = await page.screenshot(
                                                    type='jpeg',
                                                    quality=80,  # High quality JPEG
                                                    full_page=True
                                                )
                                                jpeg_b64 = base64.b64encode(jpeg_screenshot).decode('utf-8')

                                                if len(jpeg_b64) < 300000:  # 300KB limit for JPEG
                                                    screenshot_data = {
                                                        'task_id': '${taskId}',
                                                        'screenshot': jpeg_b64,
                                                        'url': url,
                                                        'title': title,
                                                        'timestamp': time.time() * 1000,
                                                        'is_thumbnail': False
                                                    }

                                                    json_data = json.dumps(screenshot_data, ensure_ascii=True)
                                                    print(f"BROWSER_USE_SCREENSHOT:{json_data}")
                                                    sys.stdout.flush()

                                                    log_progress('system', f'📸 Screenshot JPEG de alta qualidade enviado: {title}')
                                                else:
                                                    log_progress('system', f'📸 Screenshot capturado mas muito grande para enviar: {title}')
                                            except Exception as jpeg_error:
                                                log_progress('system', f'⚠️ Erro ao converter para JPEG: {str(jpeg_error)}')
                                    except Exception as resize_error:
                                        log_progress('system', f'⚠️ Erro ao redimensionar screenshot: {str(resize_error)}')
                                        
                                        # Try sending a very small thumbnail instead
                                        try:
                                            # Create a very small thumbnail
                                            img = Image.open(io.BytesIO(screenshot))
                                            img = img.resize((400, 300), Image.LANCZOS)
                                            
                                            buffer = io.BytesIO()
                                            img.save(buffer, format='JPEG', quality=20)
                                            buffer.seek(0)
                                            
                                            mini_thumbnail = base64.b64encode(buffer.read()).decode('utf-8')
                                            
                                            screenshot_data = {
                                                'task_id': '${taskId}',
                                                'screenshot': mini_thumbnail,
                                                'url': url,
                                                'title': title,
                                                'timestamp': time.time() * 1000,
                                                'is_thumbnail': True
                                            }
                                            
                                            json_data = json.dumps(screenshot_data, ensure_ascii=True)
                                            print(f"BROWSER_USE_SCREENSHOT:{json_data}")
                                            sys.stdout.flush()
                                            
                                            log_progress('system', f'📸 Mini-thumbnail enviado: {title}')
                                        except:
                                            log_progress('system', f'⚠️ Não foi possível enviar nenhum screenshot: {title}')
                                except Exception as screenshot_error:
                                    log_progress('system', f'📸 Erro ao capturar screenshot: {str(screenshot_error)}')
                            else:
                                # Use cached data
                                try:
                                    with open(cache_file, 'r') as f:
                                        cache_data = json.load(f)
                                    
                                    # Extract screenshot from cache
                                    if 'screenshot' in cache_data:
                                        screenshot_b64 = cache_data['screenshot']
                                        
                                        # Try to send a thumbnail of the cached screenshot
                                        try:
                                            from PIL import Image
                                            import io
                                            
                                            # Convert base64 to bytes
                                            screenshot = base64.b64decode(screenshot_b64)
                                            
                                            # Convert bytes to image
                                            img = Image.open(io.BytesIO(screenshot))
                                            
                                            # Create a small thumbnail
                                            img = img.resize((400, 300), Image.LANCZOS)
                                            
                                            buffer = io.BytesIO()
                                            img.save(buffer, format='JPEG', quality=20)
                                            buffer.seek(0)
                                            
                                            mini_thumbnail = base64.b64encode(buffer.read()).decode('utf-8')
                                            
                                            screenshot_data = {
                                                'task_id': '${taskId}',
                                                'screenshot': mini_thumbnail,
                                                'url': url,
                                                'title': title,
                                                'timestamp': time.time() * 1000,
                                                'is_thumbnail': True,
                                                'from_cache': True
                                            }
                                            
                                            json_data = json.dumps(screenshot_data, ensure_ascii=True)
                                            print(f"BROWSER_USE_SCREENSHOT:{json_data}")
                                            sys.stdout.flush()
                                            
                                            log_progress('system', f'📸 Screenshot do cache enviado: {title}')
                                        except Exception as thumb_error:
                                            log_progress('system', f'⚠️ Erro ao processar screenshot do cache: {str(thumb_error)}')
                                except Exception as cache_read_error:
                                    log_progress('system', f'⚠️ Erro ao ler cache: {str(cache_read_error)}')
                                    # Fall back to non-cached behavior
                                    use_cache = False
                        except Exception as cache_error:
                            log_progress('system', f'⚠️ Erro ao usar cache: {str(cache_error)}')
                        
                        # Log URL change
                        url_data = {
                            'task_id': '${taskId}',
                            'url': url,
                            'title': title,
                            'timestamp': time.time() * 1000
                        }
                        print(f"BROWSER_USE_URL:{json.dumps(url_data)}")
                        sys.stdout.flush()
                        
                except Exception as error:
                    log_progress('system', f'⚠️ Erro ao capturar informações da página: {str(error)}')
                
                log_progress('system', f'✅ Passo {step_count} concluído')
                return result
            
            # Replace the step method
            agent.step = screenshot_step`;
  }

  /**
   * Gera a seção de processamento de resultados para o script Python
   * @param taskId ID da tarefa
   * @param config Configuração da tarefa
   * @returns Seção de processamento de resultados
   */
  private static generateResultProcessingSection(taskId: string, config: BrowserTaskConfig): string {
    return `
        # Process and format the result for better presentation
        try:
            # Extract the main content from the result
            result_str = str(result)
            
            # Try to extract the final answer from the agent's response
            final_content = ""
            
            # Look for the extracted_content in the last action result
            if hasattr(result, 'all_results') and result.all_results:
                # Get the last result that has extracted_content
                for action_result in reversed(result.all_results):
                    if hasattr(action_result, 'extracted_content') and action_result.extracted_content:
                        content = action_result.extracted_content
                        # Skip technical content
                        if not any(tech in content.lower() for tech in ['data written to file', 'read from file', 'navigated to']):
                            final_content = content
                            break
            
            # If we found good content, format it nicely
            if final_content and len(final_content) > 100:
                # Clean up the content
                cleaned_content = final_content.replace('\\\\n', '\\n').replace('\\\\"', '"')
                
                # Create a structured report
                structured_report = f"""# 📋 RELATÓRIO DE PESQUISA

## 🎯 Tarefa Executada
{task}

## 📊 Resultado da Análise

{cleaned_content}

---
*Pesquisa realizada com sucesso usando ${config.model}*
"""
                
                log_progress('result', structured_report)
            else:
                # Fallback to basic result
                log_progress('result', result_str)
                
        except Exception as format_error:
            log_progress('system', f'⚠️ Erro ao formatar resultado: {str(format_error)}')
            log_progress('result', str(result))`;
  }
}