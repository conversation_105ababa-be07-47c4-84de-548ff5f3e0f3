/**
 * Utilitários para manipulação de scripts Python
 */
import { spawn } from 'child_process';
import fs from 'fs';
import { TaskManager } from './task-manager';

export class PythonScriptUtils {
  /**
   * Corrige problemas comuns de indentação em scripts Python gerados
   * @param script Conteúdo do script Python
   * @returns Script Python com indentação corrigida
   */
  static fixIndentation(script: string): string {
    // Split the script into lines for processing
    const lines = script.split('\n');
    const fixedLines: string[] = [];
    
    // Track indentation levels and blocks
    const indentStack: number[] = [0];
    let currentIndent = 0;
    
    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];
      const trimmedLine = line.trim();
      
      // Skip empty lines
      if (!trimmedLine) {
        fixedLines.push(line);
        continue;
      }
      
      // Fix conditional expressions in dictionaries
      line = this.fixConditionalExpressions(line);
      
      // Calculate leading whitespace
      const leadingSpaces = line.length - line.trimStart().length;
      
      // Check if this line should be indented (follows a line ending with :)
      if (i > 0 && lines[i-1].trim().endsWith(':')) {
        // This line should be indented relative to the previous line
        const prevLineIndent = lines[i-1].length - lines[i-1].trimStart().length;
        const expectedIndent = prevLineIndent + 4; // Python standard is 4 spaces
        
        // If the line isn't properly indented, fix it
        if (leadingSpaces <= prevLineIndent && trimmedLine !== 'else:' && 
            !trimmedLine.startsWith('elif') && !trimmedLine.startsWith('except') && 
            !trimmedLine.startsWith('finally:')) {
          // Add proper indentation
          fixedLines.push(' '.repeat(expectedIndent) + line.trim());
          currentIndent = expectedIndent;
          indentStack.push(currentIndent);
          continue;
        }
      }
      
      // Handle dedent (less indentation than previous line)
      if (leadingSpaces < currentIndent) {
        while (indentStack.length > 1 && indentStack[indentStack.length - 1] > leadingSpaces) {
          indentStack.pop();
        }
        currentIndent = indentStack[indentStack.length - 1];
      } 
      // Handle indent (more indentation than previous line)
      else if (leadingSpaces > currentIndent) {
        currentIndent = leadingSpaces;
        indentStack.push(currentIndent);
      }
      
      fixedLines.push(line);
    }
    
    return fixedLines.join('\n');
  }

  /**
   * Corrige expressões condicionais em scripts Python
   * @param line Linha de código Python
   * @returns Linha com expressões condicionais corrigidas
   */
  static fixConditionalExpressions(line: string): string {
    // Look for patterns like: "key": value if condition else other_value,
    if (line.includes(' if ') && line.includes(' else ') && line.includes(':')) {
      // Make sure the conditional expression is properly parenthesized
      const parts = line.split(':');
      if (parts.length >= 2) {
        const beforeColon = parts[0];
        const afterColon = parts.slice(1).join(':').trim();
        
        if (afterColon.includes(' if ') && afterColon.includes(' else ')) {
          // Check if it's already parenthesized
          if (!afterColon.startsWith('(') || !afterColon.includes(')')) {
            // Add parentheses around the conditional expression
            const commaPos = afterColon.lastIndexOf(',');
            if (commaPos > 0) {
              const expr = afterColon.substring(0, commaPos).trim();
              const rest = afterColon.substring(commaPos);
              return `${beforeColon}: (${expr})${rest}`;
            } else {
              return `${beforeColon}: (${afterColon})`;
            }
          }
        }
      }
    }
    
    // Fix specific syntax errors in slicing operations
    if (line.includes('[: (') && line.includes(']')) {
      // Fix malformed slice syntax like [: (10]
      return line.replace(/\[:\s*\((\d+)\]/g, '[:$1]');
    }
    
    // Fix unbalanced parentheses in conditional expressions
    if (line.includes(' if ') && line.includes(' else ')) {
      // Count opening and closing parentheses
      const openCount = (line.match(/\(/g) || []).length;
      const closeCount = (line.match(/\)/g) || []).length;
      
      if (openCount > closeCount) {
        // Add missing closing parentheses
        return line + ')'.repeat(openCount - closeCount);
      } else if (closeCount > openCount) {
        // Remove extra closing parentheses
        let result = line;
        for (let i = 0; i < (closeCount - openCount); i++) {
          const lastParenPos = result.lastIndexOf(')');
          if (lastParenPos >= 0) {
            result = result.substring(0, lastParenPos) + result.substring(lastParenPos + 1);
          }
        }
        return result;
      }
    }
    
    return line;
  }

  /**
   * Valida um script Python para erros de sintaxe antes da execução
   * @param scriptPath Caminho para o script Python
   * @param taskId ID da tarefa para logging
   */
  static async validateScript(scriptPath: string, taskId: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      // Use Python's built-in syntax checking
      const validateProcess = spawn('python3', ['-m', 'py_compile', scriptPath]);
      
      let errorOutput = '';
      
      validateProcess.stderr?.on('data', (data: Buffer) => {
        errorOutput += data.toString();
      });
      
      validateProcess.on('close', (code: number) => {
        if (code !== 0) {
          console.error(`Python syntax validation failed: ${errorOutput}`);
          
          // Try to extract the specific error
          const errorMatch = errorOutput.match(/line (\d+).*?(IndentationError|SyntaxError): (.+)/);
          
          if (errorMatch) {
            const [, lineNum, errorType, errorMsg] = errorMatch;
            
            // Try to fix the specific error
            try {
              const scriptContent = fs.readFileSync(scriptPath, 'utf8');
              const lines = scriptContent.split('\n');
              const errorLineNum = parseInt(lineNum, 10);
              
              // Log the error details
              TaskManager.addMessage(taskId, {
                type: 'system',
                content: `⚠️ ${errorType} at line ${lineNum}: ${errorMsg}`,
                timestamp: new Date().toISOString(),
                metadata: { action: 'validation_error', line: lineNum, error_type: errorType }
              });
              
              // For indentation errors, try to fix the specific line
              if (errorType === 'IndentationError' && errorLineNum > 0 && errorLineNum <= lines.length) {
                // Find the previous line that ends with a colon
                let colonLine = errorLineNum - 2;
                while (colonLine >= 0) {
                  if (lines[colonLine].trim().endsWith(':')) {
                    break;
                  }
                  colonLine--;
                }
                
                if (colonLine >= 0) {
                  const colonLineIndent = lines[colonLine].length - lines[colonLine].trimStart().length;
                  const fixedLine = ' '.repeat(colonLineIndent + 4) + lines[errorLineNum - 1].trim();
                  lines[errorLineNum - 1] = fixedLine;
                  
                  // Write the fixed script back
                  fs.writeFileSync(scriptPath, lines.join('\n'));
                  
                  // Log the fix
                  TaskManager.addMessage(taskId, {
                    type: 'system',
                    content: `🔧 Fixed indentation error at line ${lineNum}`,
                    timestamp: new Date().toISOString(),
                    metadata: { action: 'validation_fix', line: lineNum }
                  });
                  
                  // Try validation again
                  PythonScriptUtils.validateScript(scriptPath, taskId)
                    .then(resolve)
                    .catch(reject);
                  return;
                }
              }
            } catch (fixError) {
              console.error('Error trying to fix script:', fixError);
            }
          }
          
          reject(new Error(`Python script validation failed: ${errorOutput}`));
        } else {
          resolve();
        }
      });
      
      validateProcess.on('error', (error: Error) => {
        reject(new Error(`Failed to validate Python script: ${error.message}`));
      });
    });
  }
}