import { BrowserTaskConfig } from "./types";
import {
  BASE_TEMPLATE,
  IMPORTS_SECTION,
  LLM_SETUP_SECTION,
  BROWSER_SETUP_SECTION,
  DOMAIN_RESTRICTIONS_SECTION,
  AGENT_SETUP_SECTION,
  EXECUTION_SECTION,
  CONTENT_EXTRACTION_SECTION,
  CLEANUP_SECTION
} from './templates';

export class ScriptAssembler {
  static assembleScript(
    taskId: string,
    task: string,
    config: BrowserTaskConfig
  ): string {
    // Prepare replacement values
    const replacements: Record<string, string> = {
      '{{TASK_ID}}': taskId,
      '{{TASK}}': task.replace(/"/g, '\\"'),
      '{{PROVIDER}}': config.provider,
      '{{MODEL}}': config.model,
      '{{API_KEY}}': config.api_key || '',
      '{{PROVIDER_ENV_KEY}}': config.provider.toUpperCase() + '_API_KEY',
      '{{TEMPERATURE}}': config.temperature.toString(),
      '{{HEADLESS}}': config.headless ? 'True' : 'False',
      '{{USE_VISION}}': config.use_vision ? 'True' : 'False',
      '{{MAX_STEPS}}': config.max_steps.toString(),
      '{{ALLOWED_DOMAINS}}': JSON.stringify(config.allowed_domains || [])
    };

    // Verify all templates exist
    const templates = {
      BASE_TEMPLATE,
      IMPORTS_SECTION,
      LLM_SETUP_SECTION,
      BROWSER_SETUP_SECTION,
      DOMAIN_RESTRICTIONS_SECTION,
      AGENT_SETUP_SECTION,
      EXECUTION_SECTION,
      CONTENT_EXTRACTION_SECTION,
      CLEANUP_SECTION
    };

    // Check if any template is undefined
    for (const [name, template] of Object.entries(templates)) {
      if (!template) {
        throw new Error(`Template ${name} is undefined`);
      }
    }

    // Assemble sections
    const sections: Record<string, string> = {
      '{{IMPORTS_SECTION}}': this.replaceTokens(IMPORTS_SECTION, replacements),
      '{{LLM_SETUP_SECTION}}': this.replaceTokens(LLM_SETUP_SECTION, replacements),
      '{{BROWSER_SETUP_SECTION}}': this.replaceTokens(BROWSER_SETUP_SECTION, replacements),
      '{{AGENT_SETUP_SECTION}}': this.replaceTokens(AGENT_SETUP_SECTION, {
        ...replacements,
        '{{DOMAIN_RESTRICTIONS_SECTION}}': this.replaceTokens(DOMAIN_RESTRICTIONS_SECTION, replacements)
      }),
      '{{EXECUTION_SECTION}}': this.replaceTokens(EXECUTION_SECTION, replacements),
      '{{CONTENT_EXTRACTION_SECTION}}': this.replaceTokens(CONTENT_EXTRACTION_SECTION, replacements),
      '{{CLEANUP_SECTION}}': this.replaceTokens(CLEANUP_SECTION, replacements)
    };

    // Assemble final script
    return this.replaceTokens(BASE_TEMPLATE, { ...replacements, ...sections });
  }

  private static replaceTokens(template: string, replacements: Record<string, string>): string {
    let result = template;
    for (const [token, value] of Object.entries(replacements)) {
      // Fix: Properly escape special regex characters in the token
      const escapedToken = token.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      result = result.replace(new RegExp(escapedToken, 'g'), value || '');
    }
    return result;
  }
}