/**
 * Serviço de limpeza automática de arquivos temporários
 */
import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';

export class CleanupService {
  private static instance: CleanupService;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private readonly intervalMinutes = 30; // Executar limpeza a cada 30 minutos

  private constructor() {
    // Singleton
  }

  public static getInstance(): CleanupService {
    if (!CleanupService.instance) {
      CleanupService.instance = new CleanupService();
    }
    return CleanupService.instance;
  }

  /**
   * Inicia o serviço de limpeza automática
   */
  public start(): void {
    console.log('🧹 Iniciando serviço de limpeza automática de arquivos temporários');
    
    // Executar limpeza imediatamente ao iniciar
    this.cleanupTempFiles();
    
    // Configurar limpeza periódica
    this.cleanupInterval = setInterval(() => {
      this.cleanupTempFiles();
    }, this.intervalMinutes * 60 * 1000);
  }

  /**
   * Para o serviço de limpeza automática
   */
  public stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('🛑 Serviço de limpeza automática parado');
    }
  }

  /**
   * Executa a limpeza de arquivos temporários
   * @param ageMinutes Idade mínima dos arquivos a serem removidos (em minutos)
   */
  public cleanupTempFiles(ageMinutes: number = 30): void {
    const scriptPath = path.join(process.cwd(), 'cleanup_temp_files.js');
    
    // Verificar se o script existe
    if (!fs.existsSync(scriptPath)) {
      console.error('❌ Script de limpeza não encontrado:', scriptPath);
      return;
    }
    
    // Executar o script de limpeza
    const cleanupProcess = spawn('node', [scriptPath, `--age=${ageMinutes}`], {
      stdio: 'ignore', // Não mostrar saída no console
      detached: true
    });
    
    // Não esperar pelo processo
    cleanupProcess.unref();
    
    console.log(`🧹 Limpeza automática iniciada (removendo arquivos com mais de ${ageMinutes} minutos)`);
  }

  /**
   * Limpa todos os arquivos temporários imediatamente
   */
  public cleanupAllTempFiles(): void {
    const scriptPath = path.join(process.cwd(), 'cleanup_temp_files.js');
    
    // Verificar se o script existe
    if (!fs.existsSync(scriptPath)) {
      console.error('❌ Script de limpeza não encontrado:', scriptPath);
      return;
    }
    
    // Executar o script de limpeza
    const cleanupProcess = spawn('node', [scriptPath, '--all'], {
      stdio: 'ignore', // Não mostrar saída no console
      detached: true
    });
    
    // Não esperar pelo processo
    cleanupProcess.unref();
    
    console.log('🧹 Limpeza completa iniciada (removendo todos os arquivos temporários)');
  }
}