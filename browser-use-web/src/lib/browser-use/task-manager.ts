import { v4 as uuidv4 } from 'uuid'
import fs from 'fs'
import path from 'path'
import { EnhancedBrowserTask, TaskMessage, BrowserTaskConfig, ProgressData, ScreenshotData, UrlData } from './types'

// Enhanced task storage with persistence
const enhancedTasks = new Map<string, EnhancedBrowserTask>()

// Path for task storage
const TASKS_DIR = path.join(process.cwd(), '.tasks')

// Ensure tasks directory exists
try {
  if (!fs.existsSync(TASKS_DIR)) {
    fs.mkdirSync(TASKS_DIR, { recursive: true })
  }
} catch (error) {
  console.error('Error creating tasks directory:', error)
}

export class TaskManager {
  static createTask(task: string, config: BrowserTaskConfig): EnhancedBrowserTask {
    const taskId = uuidv4()
    const newTask: EnhancedBrowserTask = {
      id: taskId,
      task,
      status: 'pending',
      config,
      screenshots: [],
      messages: [
        {
          id: uuidv4(),
          type: 'system',
          content: `🚀 Starting enhanced browser task with ${config.model}`,
          timestamp: new Date().toISOString(),
          metadata: {
            action: 'task_init',
            config
          }
        }
      ],
      createdAt: new Date().toISOString(),
    }

    enhancedTasks.set(taskId, newTask)
    this.saveTask(taskId, newTask)
    return newTask
  }

  static getTask(taskId: string): EnhancedBrowserTask | undefined {
    // Try to get from memory first
    let task = enhancedTasks.get(taskId)
    
    // If not in memory, try to load from file
    if (!task) {
      task = this.loadTask(taskId)
      if (task) {
        enhancedTasks.set(taskId, task)
      }
    }
    
    return task
  }

  static getAllTasks(): EnhancedBrowserTask[] {
    // Load all tasks from files
    this.loadAllTasks()
    return Array.from(enhancedTasks.values())
  }

  static deleteTask(taskId: string): boolean {
    const result = enhancedTasks.delete(taskId)
    
    // Also delete the task file
    try {
      const taskPath = path.join(TASKS_DIR, `${taskId}.json`)
      if (fs.existsSync(taskPath)) {
        fs.unlinkSync(taskPath)
      }
    } catch (error) {
      console.error(`Error deleting task file for ${taskId}:`, error)
    }
    
    return result
  }

  static updateTaskStatus(taskId: string, status: EnhancedBrowserTask['status']): void {
    const task = this.getTask(taskId)
    if (task) {
      task.status = status
      this.saveTask(taskId, task)
    }
  }

  static addMessage(taskId: string, message: Omit<TaskMessage, 'id'>): void {
    const task = this.getTask(taskId)
    if (task) {
      task.messages.push({
        id: uuidv4(),
        ...message
      })
      this.saveTask(taskId, task)
    }
  }

  static updateTaskProgress(taskId: string, progressData: ProgressData): void {
    const taskData = this.getTask(taskId)
    if (!taskData) return

    // Ensure the type is one of the allowed values for TaskMessage
    const validType = (progressData.type === 'user' || 
                      progressData.type === 'assistant' || 
                      progressData.type === 'system' || 
                      progressData.type === 'result') 
                      ? progressData.type 
                      : 'system';
                      
    taskData.messages.push({
      id: uuidv4(),
      type: validType,
      content: progressData.content || '',
      timestamp: new Date(progressData.timestamp).toISOString(),
      metadata: progressData.metadata || {}
    })
    
    this.saveTask(taskId, taskData)
  }

  static addScreenshot(taskId: string, screenshotData: ScreenshotData): void {
    const taskData = this.getTask(taskId)
    if (!taskData) return

    taskData.screenshots.push(screenshotData.screenshot)
    taskData.currentUrl = screenshotData.url
    taskData.pageTitle = screenshotData.title

    taskData.messages.push({
      id: uuidv4(),
      type: 'system',
      content: `📸 Screenshot captured: ${screenshotData.title}`,
      timestamp: new Date(screenshotData.timestamp).toISOString(),
      metadata: {
        action: 'screenshot',
        url: screenshotData.url,
        title: screenshotData.title,
        screenshot: screenshotData.screenshot
      }
    })
    
    this.saveTask(taskId, taskData)
  }

  static updateCurrentUrl(taskId: string, urlData: UrlData): void {
    const taskData = this.getTask(taskId)
    if (!taskData) return

    taskData.currentUrl = urlData.url
    taskData.pageTitle = urlData.title
    
    this.saveTask(taskId, taskData)
  }

  static setTaskDuration(taskId: string): void {
    const task = this.getTask(taskId)
    if (task) {
      task.duration = Date.now() - new Date(task.createdAt).getTime()
      this.saveTask(taskId, task)
    }
  }

  static validateConfig(config: any): BrowserTaskConfig {
    return {
      model: config?.model || 'gpt-4o',
      provider: config?.provider || 'openai',
      temperature: config?.temperature || 0.0,
      headless: config?.headless !== undefined ? config.headless : false,
      use_vision: config?.use_vision !== undefined ? config.use_vision : true,
      max_steps: config?.max_steps || 50,
      api_key: config?.api_key,
      allowed_domains: config?.allowed_domains || [],
      system_prompt: config?.system_prompt || '',
      user_data_dir: config?.user_data_dir || '',
      disable_security: config?.disable_security !== undefined ? config.disable_security : true,
      window_width: config?.window_width || 1280,
      window_height: config?.window_height || 720,
      task_type: config?.task_type || 'research'
    }
  }

  // Helper methods for task persistence
  private static saveTask(taskId: string, task: EnhancedBrowserTask): void {
    try {
      const taskPath = path.join(TASKS_DIR, `${taskId}.json`)
      
      // For large tasks with screenshots, we might need to handle them specially
      const taskToSave = { ...task }
      
      // Limit the number of screenshots to prevent file size issues
      if (taskToSave.screenshots && taskToSave.screenshots.length > 5) {
        // Keep only the 5 most recent screenshots
        taskToSave.screenshots = taskToSave.screenshots.slice(-5)
      }
      
      fs.writeFileSync(taskPath, JSON.stringify(taskToSave, null, 2))
    } catch (error) {
      console.error(`Error saving task ${taskId}:`, error)
    }
  }

  private static loadTask(taskId: string): EnhancedBrowserTask | undefined {
    try {
      const taskPath = path.join(TASKS_DIR, `${taskId}.json`)
      if (fs.existsSync(taskPath)) {
        const taskData = fs.readFileSync(taskPath, 'utf8')
        return JSON.parse(taskData) as EnhancedBrowserTask
      }
    } catch (error) {
      console.error(`Error loading task ${taskId}:`, error)
    }
    return undefined
  }

  private static loadAllTasks(): void {
    try {
      if (fs.existsSync(TASKS_DIR)) {
        const files = fs.readdirSync(TASKS_DIR)
        for (const file of files) {
          if (file.endsWith('.json')) {
            const taskId = file.replace('.json', '')
            const task = this.loadTask(taskId)
            if (task && !enhancedTasks.has(taskId)) {
              enhancedTasks.set(taskId, task)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error loading all tasks:', error)
    }
  }
}