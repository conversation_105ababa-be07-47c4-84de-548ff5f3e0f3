/**
 * Sistema de cache de páginas para o Browser-Use
 * Armazena o conteúdo HTML e screenshots de páginas visitadas para evitar recarregá-las
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

interface CachedPage {
  url: string;
  title: string;
  content: string;
  screenshot?: string;
  timestamp: number;
}

export class PageCache {
  private static instance: PageCache;
  private cache: Map<string, CachedPage> = new Map();
  private cacheDir: string;
  private maxCacheSize: number = 50; // Número máximo de páginas em cache
  private maxCacheAge: number = 24 * 60 * 60 * 1000; // 24 horas em milissegundos

  private constructor() {
    // Criar diretório de cache se não existir
    this.cacheDir = path.join(process.cwd(), '.cache');
    try {
      if (!fs.existsSync(this.cacheDir)) {
        fs.mkdirSync(this.cacheDir, { recursive: true });
      }
    } catch (error) {
      console.error('<PERSON>rror creating cache directory:', error);
    }

    // Carregar cache do disco
    this.loadCache();
  }

  public static getInstance(): PageCache {
    if (!PageCache.instance) {
      PageCache.instance = new PageCache();
    }
    return PageCache.instance;
  }

  /**
   * Gera uma chave de cache para uma URL
   * @param url URL da página
   * @returns Chave de cache (hash MD5 da URL)
   */
  private getCacheKey(url: string): string {
    return crypto.createHash('md5').update(url).digest('hex');
  }

  /**
   * Verifica se uma página está em cache
   * @param url URL da página
   * @returns true se a página estiver em cache e não expirada
   */
  public hasPage(url: string): boolean {
    const key = this.getCacheKey(url);
    if (!this.cache.has(key)) {
      return false;
    }

    // Verificar se o cache expirou
    const cachedPage = this.cache.get(key)!;
    const now = Date.now();
    if (now - cachedPage.timestamp > this.maxCacheAge) {
      // Cache expirado, remover
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Obtém uma página do cache
   * @param url URL da página
   * @returns Página em cache ou null se não encontrada
   */
  public getPage(url: string): CachedPage | null {
    if (!this.hasPage(url)) {
      return null;
    }
    
    const key = this.getCacheKey(url);
    return this.cache.get(key) || null;
  }

  /**
   * Adiciona uma página ao cache
   * @param url URL da página
   * @param title Título da página
   * @param content Conteúdo HTML da página
   * @param screenshot Screenshot da página em base64 (opcional)
   */
  public addPage(url: string, title: string, content: string, screenshot?: string): void {
    const key = this.getCacheKey(url);
    
    // Adicionar ao cache em memória
    this.cache.set(key, {
      url,
      title,
      content,
      screenshot,
      timestamp: Date.now()
    });

    // Limitar tamanho do cache
    if (this.cache.size > this.maxCacheSize) {
      // Remover a entrada mais antiga
      let oldestKey: string | null = null;
      let oldestTime = Infinity;
      
      for (const [k, page] of this.cache.entries()) {
        if (page.timestamp < oldestTime) {
          oldestTime = page.timestamp;
          oldestKey = k;
        }
      }
      
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    // Salvar cache no disco
    this.saveCache();
  }

  /**
   * Salva o cache no disco
   */
  private saveCache(): void {
    try {
      // Converter o Map para um objeto para serialização
      const cacheObject: Record<string, CachedPage> = {};
      for (const [key, value] of this.cache.entries()) {
        // Não salvar screenshots no arquivo de cache para economizar espaço
        const { screenshot, ...pageWithoutScreenshot } = value;
        cacheObject[key] = pageWithoutScreenshot;
      }
      
      const cachePath = path.join(this.cacheDir, 'page-cache.json');
      fs.writeFileSync(cachePath, JSON.stringify(cacheObject, null, 2));
    } catch (error) {
      console.error('Error saving cache:', error);
    }
  }

  /**
   * Carrega o cache do disco
   */
  private loadCache(): void {
    try {
      const cachePath = path.join(this.cacheDir, 'page-cache.json');
      if (fs.existsSync(cachePath)) {
        const cacheData = fs.readFileSync(cachePath, 'utf8');
        const cacheObject = JSON.parse(cacheData);
        
        // Converter o objeto de volta para um Map
        for (const [key, value] of Object.entries(cacheObject)) {
          this.cache.set(key, value as CachedPage);
        }
        
        console.log(`Loaded ${this.cache.size} pages from cache`);
      }
    } catch (error) {
      console.error('Error loading cache:', error);
    }
  }

  /**
   * Limpa o cache
   */
  public clearCache(): void {
    this.cache.clear();
    this.saveCache();
  }
}