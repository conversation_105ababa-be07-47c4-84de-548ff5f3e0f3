interface BrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: Message[]
  createdAt: string
  duration?: number
}

interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
  }
}

// Global task storage
export const tasks = new Map<string, BrowserTask>()

// Helper functions
export function getTask(taskId: string): BrowserTask | undefined {
  return tasks.get(taskId)
}

export function setTask(taskId: string, task: BrowserTask): void {
  tasks.set(taskId, task)
}

export function getAllTasks(): BrowserTask[] {
  return Array.from(tasks.values())
}

export function deleteTask(taskId: string): boolean {
  return tasks.delete(taskId)
}
