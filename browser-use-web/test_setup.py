#!/usr/bin/env python3
"""
Setup e Verificação Final - Browser-Use Web Domain Restrictions
Este script verifica se tudo está configurado corretamente para usar as restrições de domínio.
"""

import os
import json
import sys
import subprocess
from pathlib import Path

def check_file_exists(file_path, description):
    """Verifica se um arquivo existe"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (não encontrado)")
        return False

def check_file_content(file_path, search_terms, description):
    """Verifica se um arquivo contém termos específicos"""
    if not os.path.exists(file_path):
        print(f"❌ {description}: arquivo não encontrado")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        found_terms = []
        missing_terms = []
        
        for term in search_terms:
            if term.lower() in content.lower():
                found_terms.append(term)
            else:
                missing_terms.append(term)
        
        if missing_terms:
            print(f"⚠️ {description}: {len(found_terms)}/{len(search_terms)} termos encontrados")
            print(f"   Faltando: {', '.join(missing_terms)}")
            return False
        else:
            print(f"✅ {description}: todos os termos encontrados")
            return True
            
    except Exception as e:
        print(f"❌ {description}: erro ao ler arquivo - {e}")
        return False

def check_dependencies():
    """Verifica dependências do projeto"""
    print("🔍 Verificando dependências...")
    
    # Verificar package.json
    package_json_path = "package.json"
    if os.path.exists(package_json_path):
        try:
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)
            
            required_deps = [
                "next", "react", "typescript", "@types/node",
                "lucide-react", "sonner", "uuid"
            ]
            
            all_deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
            
            missing_deps = []
            for dep in required_deps:
                if dep not in all_deps:
                    missing_deps.append(dep)
            
            if missing_deps:
                print(f"⚠️ Dependências faltando: {', '.join(missing_deps)}")
                return False
            else:
                print("✅ Todas as dependências necessárias estão presentes")
                return True
                
        except Exception as e:
            print(f"❌ Erro ao verificar package.json: {e}")
            return False
    else:
        print("❌ package.json não encontrado")
        return False

def check_typescript_config():
    """Verifica configuração do TypeScript"""
    print("\n🔍 Verificando configuração TypeScript...")
    
    tsconfig_path = "tsconfig.json"
    if os.path.exists(tsconfig_path):
        print("✅ tsconfig.json encontrado")
        return True
    else:
        print("❌ tsconfig.json não encontrado")
        return False

def run_tests():
    """Executa todos os testes automatizados"""
    print("\n🧪 Executando testes automatizados...")
    
    tests = [
        ("test_domain_restrictions.py", "Teste de validação de domínios"),
        ("test_integration.py", "Teste de integração completa")
    ]
    
    all_passed = True
    
    for test_file, description in tests:
        if os.path.exists(test_file):
            print(f"\n🚀 Executando {description}...")
            try:
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print(f"✅ {description}: PASSOU")
                else:
                    print(f"❌ {description}: FALHOU")
                    print(f"   Erro: {result.stderr}")
                    all_passed = False
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ {description}: TIMEOUT")
                all_passed = False
            except Exception as e:
                print(f"❌ {description}: ERRO - {e}")
                all_passed = False
        else:
            print(f"❌ {description}: arquivo não encontrado")
            all_passed = False
    
    return all_passed

def check_core_files():
    """Verifica arquivos principais da implementação"""
    print("🔍 Verificando arquivos principais...")
    
    core_files = [
        ("src/components/browser-config.tsx", "Componente de configuração"),
        ("src/lib/browser-use/python-script-generator.ts", "Gerador de scripts Python"),
        ("src/lib/browser-use/task-manager.ts", "Gerenciador de tarefas"),
        ("src/app/api/browser-use/route.ts", "API route"),
        ("src/lib/browser-use/process-handler.ts", "Processador de tarefas")
    ]
    
    all_exist = True
    for file_path, description in core_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist

def check_implementation_details():
    """Verifica detalhes específicos da implementação"""
    print("\n🔍 Verificando implementação detalhada...")
    
    checks = [
        {
            "file": "src/components/browser-config.tsx",
            "terms": ["allowed_domains", "addDomain", "removeDomain", "Badge"],
            "description": "Interface de domínios"
        },
        {
            "file": "src/lib/browser-use/python-script-generator.ts",
            "terms": ["is_domain_allowed", "RestrictedBrowserSession", "domain_restriction"],
            "description": "Lógica de restrição Python"
        },
        {
            "file": "src/lib/browser-use/task-manager.ts",
            "terms": ["allowed_domains", "validateConfig"],
            "description": "Validação de configuração"
        }
    ]
    
    all_good = True
    for check in checks:
        if not check_file_content(check["file"], check["terms"], check["description"]):
            all_good = False
    
    return all_good

def check_documentation():
    """Verifica se a documentação está completa"""
    print("\n📚 Verificando documentação...")
    
    docs = [
        ("DOMAIN_RESTRICTIONS.md", "Documentação principal"),
        ("IMPLEMENTATION_SUMMARY.md", "Resumo da implementação"),
        ("example_restricted_task.py", "Exemplo prático")
    ]
    
    all_docs = True
    for doc_file, description in docs:
        if not check_file_exists(doc_file, description):
            all_docs = False
    
    return all_docs

def generate_setup_report():
    """Gera relatório final de setup"""
    print("\n" + "=" * 60)
    print("📋 RELATÓRIO FINAL DE SETUP")
    print("=" * 60)
    
    # Verificar estrutura do projeto
    project_structure = [
        "src/",
        "src/components/",
        "src/lib/",
        "src/lib/browser-use/",
        "src/app/",
        "src/app/api/",
        "src/app/api/browser-use/"
    ]
    
    print("\n📁 ESTRUTURA DO PROJETO:")
    for folder in project_structure:
        if os.path.exists(folder):
            print(f"  ✅ {folder}")
        else:
            print(f"  ❌ {folder}")
    
    # Verificar arquivos de configuração
    config_files = [
        "package.json",
        "tsconfig.json",
        "next.config.js",
        "tailwind.config.js"
    ]
    
    print("\n⚙️ ARQUIVOS DE CONFIGURAÇÃO:")
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"  ✅ {config_file}")
        else:
            print(f"  ❌ {config_file}")

def create_quick_start_guide():
    """Cria um guia de início rápido"""
    guide_content = """# 🚀 Guia de Início Rápido - Restrições de Domínio

## Pré-requisitos
- Node.js 18+ instalado
- Python 3.8+ instalado
- API key de um provedor LLM (OpenAI, Anthropic, ou Google)

## Instalação

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Verificar setup:**
   ```bash
   python3 test_setup.py
   ```

3. **Executar testes:**
   ```bash
   python3 test_domain_restrictions.py
   python3 test_integration.py
   ```

## Uso Básico

1. **Iniciar servidor:**
   ```bash
   npm run dev
   ```

2. **Acessar interface:**
   - Abra http://localhost:3000
   - Vá para aba "Advanced"
   - Adicione domínios permitidos

3. **Configurar tarefa:**
   - Configure sua API key
   - Defina domínios restritos
   - Execute sua tarefa

## Exemplo Prático

```bash
python3 example_restricted_task.py
```

## Solução de Problemas

- **Erro de dependências:** Execute `npm install`
- **Erro de Python:** Verifique se Python 3.8+ está instalado
- **Testes falhando:** Verifique se todos os arquivos estão presentes

## Suporte

Consulte DOMAIN_RESTRICTIONS.md para documentação completa.
"""
    
    with open("QUICK_START.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Guia de início rápido criado: QUICK_START.md")

def main():
    """Função principal de verificação"""
    print("🔧 SETUP E VERIFICAÇÃO FINAL")
    print("Browser-Use Web - Domain Restrictions")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Executar todas as verificações
    checks = [
        ("Arquivos principais", check_core_files),
        ("Dependências", check_dependencies),
        ("Configuração TypeScript", check_typescript_config),
        ("Detalhes da implementação", check_implementation_details),
        ("Documentação", check_documentation),
        ("Testes automatizados", run_tests)
    ]
    
    for check_name, check_function in checks:
        print(f"\n🔍 {check_name}...")
        if not check_function():
            all_checks_passed = False
    
    # Gerar relatório final
    generate_setup_report()
    
    # Criar guia de início rápido
    create_quick_start_guide()
    
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 SETUP COMPLETO E VERIFICADO!")
        print("✅ Todas as verificações passaram")
        print("🚀 Sistema pronto para uso em produção")
        
        print("\n💡 PRÓXIMOS PASSOS:")
        print("1. Execute: npm run dev")
        print("2. Acesse: http://localhost:3000")
        print("3. Configure domínios na aba 'Advanced'")
        print("4. Teste com uma tarefa real")
        
    else:
        print("❌ ALGUMAS VERIFICAÇÕES FALHARAM!")
        print("⚠️ Revise os erros acima antes de usar o sistema")
        
        print("\n🔧 AÇÕES RECOMENDADAS:")
        print("1. Verifique se todos os arquivos estão presentes")
        print("2. Execute: npm install")
        print("3. Execute este script novamente")
    
    print(f"\n📊 Status final: {'APROVADO' if all_checks_passed else 'REPROVADO'}")
    return all_checks_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)