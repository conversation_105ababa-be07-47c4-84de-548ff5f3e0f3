#!/usr/bin/env python3
"""
MCP Configuration and setup utilities for browser-use web interface
"""

import os
import json
from typing import Dict, List, Any, Optional

class MCPConfig:
    """Configuration manager for MCP servers"""
    
    def __init__(self):
        self.config_file = os.path.expanduser("~/.browser_use_mcp_config.json")
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load MCP configuration from file"""
        default_config = {
            "enabled": True,
            "gemini_api_key": os.getenv('GOOGLE_API_KEY', ''),
            "github_token": os.getenv('GITHUB_TOKEN', ''),
            "servers": {
                "filesystem": {
                    "enabled": True,
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem"],
                    "base_path": "~/Desktop"
                },
                "github": {
                    "enabled": True,
                    "command": "npx", 
                    "args": ["-y", "@modelcontextprotocol/server-github"],
                    "requires_token": True
                },
                "sqlite": {
                    "enabled": True,
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-sqlite"],
                    "db_path": "~/browser_use.db"
                },
                "postgres": {
                    "enabled": False,
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-postgres"],
                    "connection_string": "postgresql://localhost/browser_use"
                },
                "brave_search": {
                    "enabled": False,
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-brave-search"],
                    "requires_api_key": True,
                    "api_key_env": "BRAVE_API_KEY"
                }
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults
                    default_config.update(loaded_config)
            return default_config
        except Exception as e:
            print(f"Error loading MCP config: {e}")
            return default_config
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving MCP config: {e}")
    
    def get_enabled_servers(self) -> List[Dict[str, Any]]:
        """Get list of enabled MCP servers with their configurations"""
        enabled_servers = []
        
        for server_name, server_config in self.config.get("servers", {}).items():
            if not server_config.get("enabled", False):
                continue
            
            # Check if required tokens/keys are available
            if server_config.get("requires_token") and not self.config.get("github_token"):
                continue
            
            if server_config.get("requires_api_key"):
                api_key_env = server_config.get("api_key_env")
                if not api_key_env or not os.getenv(api_key_env):
                    continue
            
            # Prepare server configuration
            server_info = {
                "name": server_name,
                "command": server_config["command"],
                "args": server_config["args"].copy()
            }
            
            # Add environment variables
            env = {}
            if server_name == "github" and self.config.get("github_token"):
                env["GITHUB_TOKEN"] = self.config["github_token"]
            elif server_config.get("requires_api_key"):
                api_key_env = server_config.get("api_key_env")
                if api_key_env and os.getenv(api_key_env):
                    env[api_key_env] = os.getenv(api_key_env)
            
            if env:
                server_info["env"] = env
            
            # Add specific configurations
            if server_name == "filesystem":
                base_path = os.path.expanduser(server_config.get("base_path", "~/Desktop"))
                server_info["args"].append(base_path)
            elif server_name == "sqlite":
                db_path = os.path.expanduser(server_config.get("db_path", "~/browser_use.db"))
                server_info["args"].extend(["--db-path", db_path])
            elif server_name == "postgres":
                conn_str = server_config.get("connection_string", "postgresql://localhost/browser_use")
                server_info["args"].append(conn_str)
            
            enabled_servers.append(server_info)
        
        return enabled_servers
    
    def is_mcp_enabled(self) -> bool:
        """Check if MCP is enabled"""
        return self.config.get("enabled", True)
    
    def get_gemini_api_key(self) -> Optional[str]:
        """Get Gemini API key"""
        return self.config.get("gemini_api_key") or os.getenv('GOOGLE_API_KEY')
    
    def set_gemini_api_key(self, api_key: str):
        """Set Gemini API key"""
        self.config["gemini_api_key"] = api_key
        self.save_config()
    
    def set_github_token(self, token: str):
        """Set GitHub token"""
        self.config["github_token"] = token
        self.save_config()
    
    def enable_server(self, server_name: str):
        """Enable a specific MCP server"""
        if server_name in self.config.get("servers", {}):
            self.config["servers"][server_name]["enabled"] = True
            self.save_config()
    
    def disable_server(self, server_name: str):
        """Disable a specific MCP server"""
        if server_name in self.config.get("servers", {}):
            self.config["servers"][server_name]["enabled"] = False
            self.save_config()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current MCP status and configuration"""
        status = {
            "mcp_enabled": self.is_mcp_enabled(),
            "gemini_configured": bool(self.get_gemini_api_key()),
            "github_configured": bool(self.config.get("github_token")),
            "enabled_servers": [],
            "available_servers": list(self.config.get("servers", {}).keys())
        }
        
        for server in self.get_enabled_servers():
            status["enabled_servers"].append({
                "name": server["name"],
                "command": server["command"],
                "has_env": "env" in server
            })
        
        return status

def setup_mcp_environment():
    """Setup MCP environment and install required packages"""
    try:
        print("Setting up MCP environment...")
        
        # Check if Node.js is available
        import subprocess
        try:
            subprocess.run(["node", "--version"], check=True, capture_output=True)
            print("✅ Node.js is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Node.js is required for MCP servers. Please install Node.js first.")
            return False
        
        # Install common MCP servers
        mcp_servers = [
            "@modelcontextprotocol/server-filesystem",
            "@modelcontextprotocol/server-github", 
            "@modelcontextprotocol/server-sqlite"
        ]
        
        for server in mcp_servers:
            try:
                print(f"Installing {server}...")
                subprocess.run(["npm", "install", "-g", server], check=True, capture_output=True)
                print(f"✅ {server} installed")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Failed to install {server}: {e}")
        
        print("✅ MCP environment setup complete")
        return True
        
    except Exception as e:
        print(f"Error setting up MCP environment: {e}")
        return False

def create_claude_desktop_config():
    """Create Claude Desktop configuration for browser-use MCP server"""
    config = {
        "mcpServers": {
            "browser-use": {
                "command": "uvx",
                "args": ["browser-use", "--mcp"],
                "env": {
                    "GOOGLE_API_KEY": "your-gemini-api-key-here"
                }
            }
        }
    }
    
    # Try to find Claude Desktop config location
    possible_paths = [
        os.path.expanduser("~/Library/Application Support/Claude/claude_desktop_config.json"),  # macOS
        os.path.expanduser("~/.config/claude/claude_desktop_config.json"),  # Linux
        os.path.expanduser("~/AppData/Roaming/Claude/claude_desktop_config.json")  # Windows
    ]
    
    for config_path in possible_paths:
        if os.path.exists(os.path.dirname(config_path)):
            try:
                # Load existing config if it exists
                existing_config = {}
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        existing_config = json.load(f)
                
                # Merge configurations
                if "mcpServers" not in existing_config:
                    existing_config["mcpServers"] = {}
                
                existing_config["mcpServers"]["browser-use"] = config["mcpServers"]["browser-use"]
                
                # Save updated config
                with open(config_path, 'w') as f:
                    json.dump(existing_config, f, indent=2)
                
                print(f"✅ Claude Desktop config updated at: {config_path}")
                print("Remember to update the GOOGLE_API_KEY in the config file!")
                return config_path
                
            except Exception as e:
                print(f"Error updating Claude Desktop config: {e}")
    
    print("❌ Could not find Claude Desktop config directory")
    print("Manual configuration required:")
    print(json.dumps(config, indent=2))
    return None

if __name__ == "__main__":
    # Demo/test the configuration
    config = MCPConfig()
    print("MCP Configuration Status:")
    print(json.dumps(config.get_status(), indent=2))
    
    print("\nEnabled servers:")
    for server in config.get_enabled_servers():
        print(f"- {server['name']}: {server['command']} {' '.join(server['args'])}")
