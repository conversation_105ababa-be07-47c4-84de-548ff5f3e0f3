#!/usr/bin/env python3
"""
Teste do Sistema Inteligente de Restrições
Este script testa o novo comportamento que finaliza graciosamente quando há muitos bloqueios.
"""

import json
import time
import requests

def test_smart_restriction_behavior():
    """Testa o comportamento inteligente com restrições muito restritivas"""
    print("🧪 Testando comportamento inteligente com restrições...")
    
    # Configuração com domínios muito restritivos (apenas um domínio específico)
    restrictive_config = {
        "task": "Pesquisar informações completas sobre inteligência artificial e machine learning",
        "config": {
            "model": "gpt-4o",
            "provider": "openai",
            "temperature": 0.0,
            "headless": False,
            "use_vision": True,
            "max_steps": 20,
            "api_key": "test-key-here",
            "allowed_domains": ["example.com"],  # Domínio muito restritivo
            "system_prompt": "",
            "user_data_dir": "",
            "disable_security": True,
            "window_width": 1280,
            "window_height": 720
        }
    }
    
    print("📋 Configuração de teste:")
    print(f"  Tarefa: {restrictive_config['task']}")
    print(f"  Domínios permitidos: {restrictive_config['config']['allowed_domains']}")
    print("  ⚠️ Esta configuração é intencionalmente muito restritiva")
    
    print("\n🎭 Comportamento esperado:")
    print("1. ✅ Sistema tentará acessar sites relevantes")
    print("2. 🚫 Múltiplos sites serão bloqueados")
    print("3. ⚠️ Após 5 tentativas bloqueadas, sistema finalizará graciosamente")
    print("4. 📋 Relatório de limitações será gerado")
    print("5. 💡 Recomendações serão fornecidas")
    
    return restrictive_config

def test_balanced_restrictions():
    """Testa configuração balanceada que deve funcionar bem"""
    print("\n🧪 Testando configuração balanceada...")
    
    balanced_config = {
        "task": "Pesquisar informações sobre Python programming",
        "config": {
            "model": "gpt-4o",
            "provider": "openai",
            "temperature": 0.0,
            "headless": False,
            "use_vision": True,
            "max_steps": 20,
            "api_key": "test-key-here",
            "allowed_domains": [
                "python.org",
                "docs.python.org", 
                "realpython.com",
                "wikipedia.org",
                "w3schools.com"
            ],
            "system_prompt": "",
            "user_data_dir": "",
            "disable_security": True,
            "window_width": 1280,
            "window_height": 720
        }
    }
    
    print("📋 Configuração balanceada:")
    print(f"  Tarefa: {balanced_config['task']}")
    print(f"  Domínios permitidos: {', '.join(balanced_config['config']['allowed_domains'])}")
    
    print("\n🎭 Comportamento esperado:")
    print("1. ✅ Sistema encontrará sites relevantes")
    print("2. 📄 Conteúdo será extraído com sucesso")
    print("3. 📋 Relatório completo será gerado")
    print("4. 🎉 Tarefa será concluída com sucesso")
    
    return balanced_config

def simulate_api_call(config, scenario_name):
    """Simula uma chamada para a API (para demonstração)"""
    print(f"\n🚀 Simulando execução: {scenario_name}")
    print("=" * 50)
    
    # Simular o que aconteceria
    allowed_domains = config['config']['allowed_domains']
    task = config['task']
    
    # URLs que o agente provavelmente tentaria acessar
    likely_urls = [
        "https://google.com/search?q=python+programming",
        "https://stackoverflow.com/questions/tagged/python",
        "https://github.com/python/cpython",
        "https://python.org",
        "https://docs.python.org",
        "https://realpython.com",
        "https://wikipedia.org/wiki/Python",
        "https://w3schools.com/python",
        "https://medium.com/python-programming",
        "https://reddit.com/r/python"
    ]
    
    blocked_count = 0
    allowed_count = 0
    
    print("🔍 Simulando tentativas de acesso:")
    
    for url in likely_urls:
        from urllib.parse import urlparse
        
        # Simular validação
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        if domain.startswith('www.'):
            domain = domain[4:]
        
        is_allowed = False
        for allowed_domain in allowed_domains:
            allowed_domain_clean = allowed_domain.lower()
            if allowed_domain_clean.startswith('www.'):
                allowed_domain_clean = allowed_domain_clean[4:]
            
            if domain == allowed_domain_clean or domain.endswith('.' + allowed_domain_clean):
                is_allowed = True
                break
        
        if is_allowed:
            allowed_count += 1
            print(f"  ✅ PERMITIDO: {url}")
        else:
            blocked_count += 1
            print(f"  🚫 BLOQUEADO: {url}")
            
            # Simular limite de bloqueios
            if blocked_count >= 5:
                print(f"\n⚠️ LIMITE DE BLOQUEIOS ATINGIDO ({blocked_count}/5)")
                print("🛑 Sistema finalizaria com relatório de limitações")
                break
    
    print(f"\n📊 Resultado da simulação:")
    print(f"  ✅ Sites permitidos: {allowed_count}")
    print(f"  🚫 Sites bloqueados: {blocked_count}")
    
    if blocked_count >= 5:
        print(f"  🎯 Status: Finalizado por limitações")
        print(f"  📋 Ação: Relatório de limitações gerado")
        return "limited"
    elif allowed_count > 0:
        print(f"  🎯 Status: Execução bem-sucedida")
        print(f"  📋 Ação: Relatório completo gerado")
        return "success"
    else:
        print(f"  🎯 Status: Nenhum site acessível")
        print(f"  📋 Ação: Erro de configuração")
        return "error"

def generate_recommendations():
    """Gera recomendações para diferentes cenários"""
    print("\n💡 RECOMENDAÇÕES PARA CONFIGURAÇÃO DE DOMÍNIOS")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "🔬 Pesquisa Acadêmica",
            "domains": [
                "scholar.google.com",
                "arxiv.org", 
                "ieee.org",
                "acm.org",
                "researchgate.net",
                "wikipedia.org"
            ],
            "description": "Para pesquisas científicas e acadêmicas"
        },
        {
            "name": "💻 Desenvolvimento de Software",
            "domains": [
                "stackoverflow.com",
                "github.com",
                "developer.mozilla.org",
                "w3schools.com",
                "docs.python.org",
                "nodejs.org"
            ],
            "description": "Para desenvolvimento e programação"
        },
        {
            "name": "📰 Pesquisa de Notícias",
            "domains": [
                "techcrunch.com",
                "wired.com",
                "arstechnica.com",
                "theverge.com",
                "reuters.com",
                "bbc.com"
            ],
            "description": "Para notícias e informações atuais"
        },
        {
            "name": "🏢 Ambiente Corporativo",
            "domains": [
                "company.com",
                "intranet.company.com",
                "docs.company.com",
                "wiki.company.com"
            ],
            "description": "Para uso interno corporativo"
        },
        {
            "name": "🌐 Pesquisa Geral (Balanceada)",
            "domains": [
                "google.com",
                "wikipedia.org",
                "stackoverflow.com",
                "github.com",
                "medium.com",
                "reddit.com"
            ],
            "description": "Configuração balanceada para pesquisas gerais"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}")
        print(f"📝 {scenario['description']}")
        print(f"🌐 Domínios sugeridos:")
        for domain in scenario['domains']:
            print(f"   • {domain}")

def main():
    """Função principal de teste"""
    print("🔧 TESTE DO SISTEMA INTELIGENTE DE RESTRIÇÕES")
    print("Browser-Use Web - Smart Domain Restrictions")
    print("=" * 60)
    
    # Teste 1: Configuração muito restritiva
    restrictive_config = test_smart_restriction_behavior()
    restrictive_result = simulate_api_call(restrictive_config, "Configuração Restritiva")
    
    # Teste 2: Configuração balanceada
    balanced_config = test_balanced_restrictions()
    balanced_result = simulate_api_call(balanced_config, "Configuração Balanceada")
    
    # Gerar recomendações
    generate_recommendations()
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📋 RESUMO DOS TESTES")
    print("=" * 60)
    
    print(f"\n🧪 Teste 1 - Configuração Restritiva:")
    print(f"   Status: {restrictive_result}")
    if restrictive_result == "limited":
        print(f"   ✅ Sistema funcionou corretamente - finalizou graciosamente")
        print(f"   📋 Relatório de limitações seria gerado")
        print(f"   💡 Recomendações seriam fornecidas")
    
    print(f"\n🧪 Teste 2 - Configuração Balanceada:")
    print(f"   Status: {balanced_result}")
    if balanced_result == "success":
        print(f"   ✅ Sistema funcionou corretamente - execução bem-sucedida")
        print(f"   📋 Relatório completo seria gerado")
        print(f"   🎉 Tarefa seria concluída com sucesso")
    
    print(f"\n🎯 MELHORIAS IMPLEMENTADAS:")
    print(f"   ✅ Detecção inteligente de bloqueios excessivos")
    print(f"   ✅ Finalização graciosamente após 5 tentativas bloqueadas")
    print(f"   ✅ Relatório de limitações automático")
    print(f"   ✅ Recomendações para melhorar configuração")
    print(f"   ✅ Evita loops infinitos de tentativas")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print(f"   1. Teste com configuração real usando npm run dev")
    print(f"   2. Configure domínios apropriados para sua tarefa")
    print(f"   3. Monitore logs para ajustar configurações")
    print(f"   4. Use as recomendações de domínios acima")

if __name__ == "__main__":
    main()