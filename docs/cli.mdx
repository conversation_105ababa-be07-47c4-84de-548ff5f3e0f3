---
title: "CLI"
description: "Start using the Browser Use CLI"
icon: "terminal"
---

# CLI Usage

The `browser-use` command-line interface provides multiple modes of operation for browser automation.

## Installation

Get started with browser-use immediately using `uvx`:

```bash
uvx browser-use --help
```

Or install it globally:

```bash
uv tool install 'browser-use[cli]'
```

## Modes of Operation

### 1. Interactive TUI Mode (Default)

Launch an interactive terminal UI where you can chat with the browser automation agent:

```bash
uvx browser-use
```

This opens a chat interface where you can:
- Type natural language commands to control the browser
- See real-time feedback from the agent
- View browser state and actions being performed

### 2. One-Shot Mode

Execute a single task without entering interactive mode:

```bash
uvx browser-use -p "Search for OpenAI documentation and take a screenshot"
```

Options:
- `-p, --prompt`: The task to execute
- `--headless`: Run browser in headless mode
- `--model`: Specify LLM model (default: gpt-4o)

### 3. MCP Server Mode

Run browser-use as a Model Context Protocol server:

```bash
uvx browser-use --mcp   # exoects MCP JSON RPC over stdio
```

This mode exposes browser automation capabilities as MCP tools that can be used by:
- <PERSON>op
- Other MCP-compatible clients
- Custom applications using the MCP SDK

For MCP integration details, see:
- [MCP Server Documentation](/customize/mcp-server)
- [MCP Client Documentation](/customize/mcp-client)

## Configuration

Browser-use can be configured through environment variables and a configuration file.

### Configuration File Location

The default configuration file is located at:
- `~/.config/browseruse/config.json`

You can override this location with:
- `BROWSER_USE_CONFIG_PATH` environment variable
- `BROWSER_USE_CONFIG_DIR` environment variable (directory containing config.json)

### Configuration File Format

The configuration uses a database-style format with UUID entries:

```json
{
  "browser_profile": {
    "550e8400-e29b-41d4-a716-************": {
      "id": "550e8400-e29b-41d4-a716-************",
      "default": true,
      "created_at": "2024-01-01T00:00:00",
      "headless": false,
      "user_data_dir": null,
      "allowed_domains": ["example.com"],
      "downloads_path": "~/Downloads/browser-use"
    }
  },
  "llm": {
    "6ba7b810-9dad-11d1-80b4-00c04fd430c8": {
      "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
      "default": true,
      "created_at": "2024-01-01T00:00:00",
      "api_key": "your-openai-api-key-here",
      "model": "gpt-4o",
      "temperature": 0.7
    }
  },
  "agent": {
    "6ba7b812-9dad-11d1-80b4-00c04fd430c8": {
      "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8",
      "default": true,
      "created_at": "2024-01-01T00:00:00",
      "max_steps": 100,
      "use_vision": true
    }
  }
}
```

Each configuration type (browser_profile, llm, agent) can have multiple entries, with one marked as `default: true`.

### Environment Variables

Environment variables always override config.json values:

#### General Settings
- `BROWSER_USE_LOGGING_LEVEL`: Logging level (debug, info, warning, error)
- `BROWSER_USE_CONFIG_PATH`: Full path to config.json file
- `BROWSER_USE_CONFIG_DIR`: Directory containing config.json

#### Browser Profile Settings
- `BROWSER_USE_HEADLESS`: Run browser in headless mode (true/false)
- `BROWSER_USE_ALLOWED_DOMAINS`: Comma-separated list of allowed domains
- `BROWSER_USE_USER_DATA_DIR`: Chrome user data directory path

#### LLM Settings
- `OPENAI_API_KEY`: OpenAI API key
- `ANTHROPIC_API_KEY`: Anthropic API key
- `BROWSER_USE_LLM_MODEL`: LLM model to use (e.g., gpt-4o, claude-3-opus)

#### MCP-Specific Settings
When running in MCP mode, these environment variables are particularly useful:
- `BROWSER_USE_HEADLESS`: Control browser visibility
- `OPENAI_API_KEY`: Required for agent-based tools

### Browser Profiles Directory

Browser profiles are stored in:
```
~/.config/browseruse/profiles/
├── default/           # Default browser profile
├── work/             # Custom profile example
└── research/         # Another custom profile
```

Each profile directory contains Chrome user data, allowing you to:
- Maintain separate browser sessions
- Keep cookies and local storage isolated
- Use different extensions per profile

## Examples

### Basic Usage

```bash
# Interactive mode
uvx browser-use

# One-shot task
uvx browser-use -p "Go to github.com and search for browser-use"

# Headless one-shot
uvx browser-use --headless -p "Extract prices from example.com/products"
```

### With Configuration

```bash
# Use specific config file
BROWSER_USE_CONFIG_PATH=~/my-config.json uvx browser-use

# Override settings via environment
BROWSER_USE_HEADLESS=true OPENAI_API_KEY=sk-... uvx browser-use -p "Check my email"

# Use different LLM model
BROWSER_USE_LLM_MODEL=gpt-4-turbo uvx browser-use
```

### MCP Server Usage

```bash
# Start MCP server
uvx browser-use --mcp

# With custom settings
BROWSER_USE_HEADLESS=false OPENAI_API_KEY=sk-... uvx browser-use --mcp
```

For Claude Desktop integration, add to your Claude Desktop config:

```json
{
  "mcpServers": {
    "browser-use": {
      "command": "uvx",
      "args": ["browser-use", "--mcp"],
      "env": {
        "OPENAI_API_KEY": "sk-...",
        "BROWSER_USE_HEADLESS": "false"
      }
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Browser not launching**: Ensure Chrome/Chromium is installed
2. **API key errors**: Set appropriate API key environment variables
3. **Permission errors**: Check file permissions in `~/.config/browseruse/`

### Debug Mode

Enable debug logging for troubleshooting:

```bash
BROWSER_USE_LOGGING_LEVEL=debug uvx browser-use
```

## See Also

- [Getting Started](/quickstart)
- [MCP Server Documentation](/customize/mcp-server)
- [MCP Client Documentation](/customize/mcp-client)
- [Browser Settings](/customize/browser-settings)
