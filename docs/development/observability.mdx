---
title: "Observability"
description: "Trace Browser Use's agent execution steps and browser sessions"
icon: "eye"
---

## Overview

Browser Use has a native integration with [Laminar](https://lmnr.ai) - open-source platform for tracing, evals and labeling of AI agents.
Read more about Lamina<PERSON> in the [Laminar docs](https://docs.lmnr.ai).

<Note>
  Laminar excels at tracing browser agents by providing unified visibility into
  both browser session recordings and agent execution steps.
</Note>

## Setup

To setup Laminar, you need to install the `lmnr` package and set the `LMNR_PROJECT_API_KEY` environment variable.

To get your project API key, you can either:

- Register on [Laminar Cloud](https://lmnr.ai) and get the key from your project settings
- Or spin up a local Laminar instance and get the key from the settings page

```bash
pip install 'lmnr[all]'
export LMNR_PROJECT_API_KEY=<your-project-api-key>
```

## Usage

Then, you simply initialize the Laminar at the top of your project and both Browser Use and session recordings will be automatically traced.

```python {5-8}
from browser_use.llm import ChatOpenAI
from browser_use import Agent
import asyncio

from lmnr import Laminar, Instruments
# this line auto-instruments Browser Use and any browser you use (local or remote)
Laminar.initialize(project_api_key="...", disable_batch=True, disabled_instruments={Instruments.BROWSER_USE}) # you can also pass project api key here

async def main():
    agent = Agent(
        task="open google, search Laminar AI",
        llm=ChatOpenAI(model="gpt-4.1-mini"),
    )
    result = await agent.run()
    print(result)

asyncio.run(main())
```

## Viewing Traces

You can view traces in the Laminar UI by going to the traces tab in your project.
When you select a trace, you can see both the browser session recording and the agent execution steps.

Timeline of the browser session is synced with the agent execution steps, timeline highlights indicate the agent's current step synced with the browser session.
In the trace view, you can also see the agent's current step, the tool it's using, and the tool's input and output. Tools are highlighted in the timeline with a yellow color.

<img className="block" src="/images/laminar.png" alt="Laminar" />

## Laminar

To learn more about tracing and evaluating your browser agents, check out the [Laminar docs](https://docs.lmnr.ai).
