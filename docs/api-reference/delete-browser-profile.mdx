---
title: "Delete Browser Profile For User"
api: "POST /api/v1/delete-browser-profile-for-user"
description: "Deletes the browser profile for the user"
---

Deletes the browser profile for the user. This is useful when you want to start with a fresh browser state, clearing all stored cookies, sessions, and browser data.

## Response

The endpoint returns an empty response body with a 200 status code on success.

<RequestExample>

```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

response = requests.post(f'{BASE_URL}/delete-browser-profile-for-user', headers=HEADERS)
if response.status_code == 200:
    print("Browser profile deleted successfully")
else:
    print(f"Error deleting browser profile: {response.status_code}")
```

```bash curl
curl --request POST \
  --url https://api.browser-use.com/api/v1/delete-browser-profile-for-user \
  --header 'Authorization: Bearer <token>'
```

</RequestExample>

<ResponseExample>

```json 200
{}
```

</ResponseExample>

## What Gets Deleted

When you delete your browser profile, the following data is removed:

- **Cookies**: All stored cookies from previous sessions
- **Session data**: Login sessions and authentication tokens
- **Browser cache**: Cached resources and files
- **Local storage**: Any data stored in browser local storage
- **Browser history**: Previous browsing history within the automation environment

## Usage Notes

- This action affects all future tasks until you save browser data again
- Useful for testing scenarios where you need a clean browser state
- Does not affect tasks that are currently running
- Browser profile deletion is immediate and cannot be undone

<Info>
This is particularly useful when you need to test login flows or scenarios that require a fresh browser state without any cached data.
</Info> 
