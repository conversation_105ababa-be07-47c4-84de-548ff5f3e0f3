---
title: "Resume Task"
api: "PUT /api/v1/resume-task"
description: "Resumes execution of a previously paused task"
---

Resumes execution of a previously paused task. The task will continue from where it was paused. You can't resume a stopped task.

## Parameters

<ParamField query="task_id" type="string" required>
  ID of the task to resume
</ParamField>

## Response

The endpoint returns an empty response body with a 200 status code on success.

<RequestExample>

```python python
import requests

url = "https://api.browser-use.com/api/v1/resume-task"
params = {"task_id": "task_1234567890abcdef"}
headers = {"Authorization": "Bearer <token>"}

response = requests.request("PUT", url, headers=headers, params=params)

print(response.text)
```

```bash cURL
curl --request PUT \
  --url 'https://api.browser-use.com/api/v1/resume-task?task_id=task_1234567890abcdef' \
  --header 'Authorization: Bearer <token>'
```

```javascript javascript 
const options = {method: 'PUT', headers: {Authorization: 'Bearer <token>'}};

fetch('https://api.browser-use.com/api/v1/resume-task?task_id=task_1234567890abcdef', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));
```
</RequestExample>

<ResponseExample>
    ```json 200
    {}
    ```

    ```json 422
    {
    "detail": [
        {
        "loc": [
            "query",
            "task_id"
        ],
        "msg": "field required",
        "type": "value_error.missing"
        }
    ]
    }
    ```
</ResponseExample>

## Usage Notes

- Only paused tasks can be resumed
- The task status will change from "paused" to "running"
- Browser automation will continue from where it was paused
- Stopped tasks cannot be resumed - you must create a new task instead

<Warning>
You cannot resume a task that has been stopped. Only paused tasks can be resumed.
</Warning>
