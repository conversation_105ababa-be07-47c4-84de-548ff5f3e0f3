---
title: "API Reference"
description: "Complete reference documentation for the Browser Use Cloud API"
icon: "code"
---

# Browser Use Cloud API

The Browser Use Cloud API allows you to programmatically control browser automation tasks. This API enables you to:

- Create and manage browser automation tasks
- Schedule recurring tasks
- Monitor task execution and status
- Retrieve task outputs and media
- Manage browser profiles and user data
- Upload and manage files for task execution

## Base URL

All API requests should be made to the following base URL:

```
https://api.browser-use.com
```

## Authentication

All API endpoints (except `/ping`) require authentication using a Bearer token. See the [Authentication](/api-reference/authentication) section for details.

## Rate Limits

The API is subject to rate limiting based on your subscription plan. If you exceed the rate limit, you'll receive a `429 Too Many Requests` response.

## API Endpoints

### Task Management

Core endpoints for creating and managing browser automation tasks:

- [**Run Task**](/api-reference/run-task) - `POST /api/v1/run-task` - Create a new browser automation task
- [**Get Task**](/api-reference/get-task) - `GET /api/v1/task/{task_id}` - Get comprehensive task information
- [**Get Task Status**](/api-reference/get-task-status) - `GET /api/v1/task/{task_id}/status` - Get current task status
- [**List Tasks**](/api-reference/list-tasks) - `GET /api/v1/tasks` - Get paginated list of all tasks
- [**Stop Task**](/api-reference/stop-task) - `PUT /api/v1/stop-task` - Stop a running task
- [**Pause Task**](/api-reference/pause-task) - `PUT /api/v1/pause-task` - Pause task execution
- [**Resume Task**](/api-reference/resume-task) - `PUT /api/v1/resume-task` - Resume a paused task

### Task Media & Files

Endpoints for retrieving task-generated media and files:

- [**Get Task Media**](/api-reference/get-task-media) - `GET /api/v1/task/{task_id}/media` - Get task recordings
- [**Get Task Screenshots**](/api-reference/get-task-screenshots) - `GET /api/v1/task/{task_id}/screenshots` - Get task screenshots
- [**Get Task GIF**](/api-reference/get-task-gif) - `GET /api/v1/task/{task_id}/gif` - Get animated task GIF
- [**Get Task Output File**](/api-reference/files#get-task-output-file) - `GET /api/v1/task/{task_id}/output-file/{file_name}` - Download task output files

### Scheduled Tasks

Endpoints for managing recurring browser automation tasks:

- [**Create Scheduled Task**](/api-reference/scheduled-tasks#create-scheduled-task) - `POST /api/v1/scheduled-task` - Create a new scheduled task
- [**List Scheduled Tasks**](/api-reference/scheduled-tasks#list-scheduled-tasks) - `GET /api/v1/scheduled-tasks` - Get paginated list of scheduled tasks
- [**Get Scheduled Task**](/api-reference/scheduled-tasks#get-scheduled-task) - `GET /api/v1/scheduled-task/{task_id}` - Get scheduled task details
- [**Update Scheduled Task**](/api-reference/scheduled-tasks#update-scheduled-task) - `PUT /api/v1/scheduled-task/{task_id}` - Update scheduled task
- [**Delete Scheduled Task**](/api-reference/scheduled-tasks#delete-scheduled-task) - `DELETE /api/v1/scheduled-task/{task_id}` - Delete scheduled task

### File Management

Endpoints for uploading and managing files for task execution:

- [**Upload File Presigned URL**](/api-reference/files#upload-file-presigned-url) - `POST /api/v1/uploads/presigned-url` - Get presigned URL for file upload

### User Management

Endpoints for managing your account, browser profiles, and checking credits:

- [**Check Balance**](/api-reference/user#check-balance) - `GET /api/v1/balance` - Check API credit balance
- [**Verify Authentication**](/api-reference/user#verify-authentication) - `GET /api/v1/me` - Verify API key validity
- [**Delete Browser Profile**](/api-reference/user#delete-browser-profile) - `POST /api/v1/delete-browser-profile-for-user` - Delete browser profile
- [**Ping**](/api-reference/user#ping) - `GET /api/v1/ping` - Health check endpoint

## Response Format

All responses are returned in JSON format. Successful responses will have a 2xx status code, while errors will have a 4xx or 5xx status code.

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of a request. In case of an error, the response body will contain additional information about the error. See the [Error Handling](/api-reference/errors) section for details.

## Additional Resources

- [**API Models**](/api-reference/models) - Complete reference for all data models and schemas
- [**Authentication**](/api-reference/authentication) - Learn how to authenticate API requests
- [**Rate Limits**](/api-reference/rate-limits) - Understand API rate limiting
- [**Examples**](/api-reference/examples) - Code examples and common use cases
- [**SDKs**](/api-reference/sdks) - Available SDKs and client libraries

## Getting Started

1. **Get your API key** from the [Browser Use Cloud dashboard](https://cloud.browser-use.com/settings/api-keys)
2. **Verify your authentication** using the `/api/v1/me` endpoint
3. **Create your first task** using the `/api/v1/run-task` endpoint
4. **Monitor task progress** using the `/api/v1/task/{task_id}` endpoint

For more detailed examples and tutorials, see the [Examples](/api-reference/examples) section.
