---
title: "Get Task GIF"
api: "GET /api/v1/task/{task_id}/gif"
description: "Get an animated GIF of the task execution"
---

Returns a GIF URL generated from the screenshots of the task execution. Only available for completed tasks that have screenshots.

<ParamField path="task_id" type="string" required>
  ID of the task to retrieve GIF for
</ParamField>

<ResponseField name="gif" type="string">
  URL to the animated GIF showing the task execution
</ResponseField>

<RequestExample>
```python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

task_id = 'task_1234567890abcdef'
response = requests.get(f'{BASE_URL}/task/{task_id}/gif', headers=HEADERS)
gif_data = response.json()

if gif_data['gif']:
    print(f"GIF available at: {gif_data['gif']}")
    
    # Download the GIF
    gif_response = requests.get(gif_data['gif'])
    with open('task_execution.gif', 'wb') as f:
        f.write(gif_response.content)
else:
    print("No GIF available for this task")
```
</RequestExample>

<ResponseExample>
```json
{
  "gif": "https://media.browser-use.com/gifs/task_1234567890abcdef/execution.gif"
}
```
</ResponseExample>

## GIF Generation

The GIF is automatically generated from task screenshots:

- **Sequential frames**: Screenshots are combined in chronological order
- **Optimized timing**: Frame duration is optimized for readability
- **Compressed format**: GIFs are compressed for faster loading
- **Full viewport**: Shows the complete browser viewport for each step

## Availability

- GIFs are only available for completed tasks
- Requires at least 2 screenshots to generate a GIF
- Generation happens automatically after task completion
- Files are stored for 30 days after task completion

## Use Cases

Task GIFs are useful for:
- Visual task summaries and reports
- Debugging and troubleshooting
- Sharing task execution with team members
- Creating documentation of automated processes

<Note>
GIF generation may take a few minutes after task completion. If the GIF is not immediately available, check again after a short delay.
</Note> 
