---
title: "Get Task Status"
api: "GET /api/v1/task/{task_id}/status"
description: "Get the current status of a task"
---

Returns just the current status of a task (created, running, finished, stopped, paused, or failed). This is more lightweight than the full task details endpoint.

## Path Parameters

<ParamField path="task_id" type="string" required>
  ID of the task to check status for
</ParamField>

## Response

The endpoint returns the status as a simple string value (not wrapped in an object).

<RequestExample>
```python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

task_id = 'task_1234567890abcdef'
response = requests.get(f'{BASE_URL}/task/{task_id}/status', headers=HEADERS)
status = response.json()
print(f"Task status: {status}")
```

```bash curl
curl --request GET \
  --url https://api.browser-use.com/api/v1/task/{task_id}/status \
  --header 'Authorization: Bearer <token>'
```
</RequestExample>

<ResponseExample>
```json 200
"finished"
```

```json 404
{
  "detail": "Task not found"
}
```

```json 422
{
  "detail": [
    {
      "loc": ["path", "task_id"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```
</ResponseExample>

## Status Values

The status field can have one of the following values:

- `created`: Task is initialized but not yet started
- `running`: Task is currently executing
- `finished`: Task has completed successfully
- `stopped`: Task was manually stopped
- `paused`: Task execution is temporarily paused
- `failed`: Task encountered an error and could not complete

## Use Cases

This endpoint is useful for:
- Polling task status without retrieving full task details
- Lightweight status checks in monitoring applications
- Quick status verification before making other API calls
- Building real-time dashboards with minimal data transfer

<Tip>
Use this endpoint instead of the full task details endpoint when you only need to check the current status, as it's much faster and uses less bandwidth.
</Tip> 
