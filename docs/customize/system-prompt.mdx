---
title: "System Prompt"
description: "Customize the system prompt to control agent behavior and capabilities"
icon: "message"
---

## Overview

You can customize the system prompt in two ways:

1. Extend the default system prompt with additional instructions
2. Override the default system prompt entirely

<Note>
  Custom system prompts allow you to modify the agent's behavior at a
  fundamental level. Use this feature carefully as it can significantly impact
  the agent's performance and reliability.
</Note>

### Extend System Prompt (recommended)

To add additional instructions to the default system prompt:

```python
extend_system_message = """
REMEMBER the most important RULE:
ALWAYS open first a new tab and go first to url wikipedia.com no matter the task!!!
"""
```

### Override System Prompt

<Warning>
  Not recommended! If you must override the [default system
  prompt](https://github.com/browser-use/browser-use/blob/main/browser_use/agent/system_prompt.md),
  make sure to test the agent yourself.
</Warning>

Anyway, to override the default system prompt:

```python
# Define your complete custom prompt
override_system_message = """
You are an AI agent that helps users with web browsing tasks.

[Your complete custom instructions here...]
"""

# Create agent with custom system prompt
agent = Agent(
    task="Your task here",
    llm=ChatOpenAI(model='gpt-4'),
    override_system_message=override_system_message
)
```

### Extend Planner System Prompt

You can customize the behavior of the planning agent by extending its system prompt:

```python
extend_planner_system_message = """
PRIORITIZE gathering information before taking any action.
Always suggest exploring multiple options before making a decision.
"""

# Create agent with extended planner system prompt
llm = ChatOpenAI(model='gpt-4o')
planner_llm = ChatOpenAI(model='gpt-4o-mini')

agent = Agent(
	task="Your task here",
	llm=llm,
	planner_llm=planner_llm,
	extend_planner_system_message=extend_planner_system_message
)
```
