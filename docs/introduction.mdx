---
title: "Introduction"
description: "Welcome to Browser Use - We enable AI to control your browser"
icon: "book-open"
---

<img className="block" src="/images/browser-use.png" alt="Browser Use" />

## Overview

Browser Use is the easiest way to connect your AI agents with the browser. It makes websites accessible for AI agents by providing a powerful, yet simple interface for browser automation.

<Note>
  If you have used Browser Use for your project, feel free to show it off in our
  [Discord community](https://link.browser-use.com/discord)!
</Note>

## Getting Started

<CardGroup cols={2}>
  <Card title="Quick Start" icon="rocket" href="/quickstart">
    Get up and running with Browser Use in minutes
  </Card>
  <Card
    title="Supported Models"
    icon="robot"
    href="/customize/supported-models"
  >
    Configure different LLMs for your agents
  </Card>
  <Card title="Agent Settings" icon="gear" href="/customize/agent-settings">
    Learn how to configure and customize your agents
  </Card>
  <Card title="Custom Functions" icon="code" href="/customize/custom-functions">
    Extend functionality with custom actions
  </Card>
</CardGroup>

## Fancy Demos

### Writing in Google Docs

Task: Write a letter in Google Docs to my <PERSON>, thanking him for everything, and save the document as a PDF.

<Frame>
  <img src="https://github.com/user-attachments/assets/242ade3e-15bc-41c2-988f-cbc5415a66aa" />
</Frame>

### Job Applications

Task: Read my CV & find ML jobs, save them to a file, and then start applying for them in new tabs.

<Frame>
  <video
    controls
    src="https://github.com/user-attachments/assets/171fb4d6-0355-46f2-863e-edb04a828d04"
  />
</Frame>

### Flight Search

Task: Find flights on kayak.com from Zurich to Beijing.

<Frame>
  <img src="https://github.com/user-attachments/assets/ea605d4a-90e6-481e-a569-f0e0db7e6390" />
</Frame>

### Data Collection

Task: Look up models with a license of cc-by-sa-4.0 and sort by most likes on Hugging Face, save top 5 to file.

<Frame>
  <video
    controls
    src="https://github.com/user-attachments/assets/de73ee39-432c-4b97-b4e8-939fd7f323b3"
  />
</Frame>

## Community & Support

<CardGroup cols={2}>
  <Card
    title="Join Discord"
    icon="discord"
    href="https://link.browser-use.com/discord"
  >
    Join our community for support and showcases
  </Card>
  <Card
    title="GitHub"
    icon="github"
    href="https://github.com/browser-use/browser-use"
  >
    Star us on GitHub and contribute to development
  </Card>
</CardGroup>

<Note>
  Browser Use is MIT licensed and actively maintained. We welcome contributions
  and feedback from the community!
</Note>
