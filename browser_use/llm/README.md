# Browser Use LLMs

We officially support the following LLMs:

- OpenAI
- Anthropic
- Google
- Groq
- Ollama
- DeepSeek

## Migrating from LangChain

Because of how we implemented the LLMs, we can technically support anything. If you want to use a LangChain model, you can use the `ChatLangchain` (NOT OFFICIALLY SUPPORTED) class.

You can find all the details in the [LangChain example](examples/models/langchain/example.py). We suggest you grab that code and use it as a reference.
